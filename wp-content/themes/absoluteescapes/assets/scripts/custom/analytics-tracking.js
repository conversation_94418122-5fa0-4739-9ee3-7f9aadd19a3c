// Debug mode - set to false to disable console logging
const DEBUG_MODE = false;

// Debug logging helper
const debug = {
  log: (...args) => DEBUG_MODE && console.log(...args),
  warn: (...args) => DEBUG_MODE && console.warn(...args)
};

/**
 * Track events and send data to Google Analytics.
 *
 * @param {string|null} triggerEvent - The event to listen for (e.g., 'click', 'mousedown', 'submit', etc.). Pass null or an empty string to fire the event directly.
 * @param {string[]|string|null} selector - CSS selector(s) for the elements to track. Can be a single selector string or array of selectors. Pass null or an empty string to fire the event directly.
 * @param {string} eventType - The event type to fire (e.g., 'button_click', 'submit', etc.).
 * @param {Object} eventData - Optional event data to use when firing the event directly.
 */
function trackEvents(triggerEvent, selector, eventType, eventData = {}) {
  debug.log('🎯 Initializing tracking:', { triggerEvent, selector, eventType, eventData });

  // If no triggerEvent or selector is provided, fire the event directly
  if (!triggerEvent && !selector) {
    debug.log('📡 Direct event firing mode');
    if (typeof gtag === 'function') {
      debug.log('📊 Sending direct event to GA:', { eventType, eventData });
      gtag('event', eventType, eventData);
    } else {
      debug.warn('⚠️ gtag not found - event not sent');
    }
    return;
  }

  // Convert single selector to array for consistent handling
  const selectors = Array.isArray(selector) ? selector : [selector];
  debug.log('🎯 Tracking selectors:', selectors);

  // Find all matching elements
  const elements = document.querySelectorAll(selectors.join(','));
  debug.log(`🔍 Found ${elements.length} elements to track`);

  // Add click listeners to each matching element
  elements.forEach(element => {
    element.addEventListener(triggerEvent, function(event) {
      debug.log('🎯 Tracked element clicked:', element);
      
      // Get the element data
      const elementData = {
        'text': element.textContent.replace(/[\n\r\s]+/g, ' ').trim(),
        'classes': element.classList.value,
        'url': element.href || (element.querySelector('a[href^="tel:"]') && element.querySelector('a[href^="tel:"]').href) || '',
        'id': element.id
      };

      debug.log('📊 Element data:', elementData);

      // Check if the gtag function exists
      if (typeof gtag === 'function') {
        const finalEventData = {
          ...elementData,
          button_name: elementData.text,
          screen_name: window.location.pathname,
          ...eventData
        };
        debug.log('📡 Sending event to GA:', { eventType, ...finalEventData });
        gtag('event', eventType, finalEventData);
      } else {
        debug.warn('⚠️ gtag not found - event not sent');
      }
    });
  });
}

debug.log('🚀 Analytics tracking script loaded');

// Initialize tracking when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  debug.log('📱 Setting up phone link tracking');
  // Track phone links in header and footer
  trackEvents('click', [
    '.masthead__details-item.masthead__phone',
    '.mastfoot__contact-item.mastfoot__phone',
    '.listing-cta.cta-top a[href^="tel:"]',
    '.listing-cta.cta-bottom a[href^="tel:"]'
  ], 'button_click');
});

// Track Gravity Forms submissions
jQuery(document).on('gform_confirmation_loaded', function(event, formId) {
  debug.log('📝 Gravity Form confirmation loaded:', formId);
  // Only track forms 1 and 7
  if (formId === 1 || formId === 7) {
    debug.log('📝 Processing form submission:', formId);
    const wrapper = jQuery(`#gform_confirmation_wrapper_${formId}`);
    const message = wrapper.text().replace(/[\n\r\s]+/g, ' ').trim();
    
    // Prepare event parameters
    const eventParams = {
      form_id: formId,
      form_message: message,
      screen_name: window.location.pathname
    };
    
    debug.log('📝 Form submitted with parameters:', eventParams);
    
    // Fire the event directly
    if (typeof gtag === 'function') {
      gtag('event', 'form_submit', eventParams);
    }
  } else {
    debug.log('ℹ️ Ignoring non-tracked form:', formId);
  }
}); 