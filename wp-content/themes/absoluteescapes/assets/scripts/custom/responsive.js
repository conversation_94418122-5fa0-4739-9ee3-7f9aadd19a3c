$(function() {


	$(".filter-links").move({
		breakpoint: 991,
		oldLocation: ".holidays-results",
		newLocation: ".filter",
		methods: {
			o: "insertBefore",
			n: "insertAfter"
		}
	});

	$(".holidays-results__sort-by-wrapper").move({
		breakpoint: 991,
		oldLocation: ".holidays-results__order",
		newLocation: ".holidays-results__sort-by-trigger-wrapper",
		methods: {
			o: "appendTo",
			n: "appendTo"
		}
	});

	$('.masthead__form-header').move({
		breakpoint: 1160,
		oldLocation: ".masthead__details",
		newLocation: ".form__container",
		methods: {
			o: "insertBefore",
			n: "prependTo"
		}
	});

});
