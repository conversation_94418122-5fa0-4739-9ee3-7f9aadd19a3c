!function(e){"use strict";e=e&&e.hasOwnProperty("default")?e.default:e;!function(){function e(e){this.value=e}function n(n){function o(r,i){try{var a=n[r](i),s=a.value;s instanceof e?Promise.resolve(s.value).then(function(e){o("next",e)},function(e){o("throw",e)}):t(a.done?"return":"normal",a.value)}catch(e){t("throw",e)}}function t(e,n){switch(e){case"return":r.resolve({value:n,done:!0});break;case"throw":r.reject(n);break;default:r.resolve({value:n,done:!1})}(r=r.next)?o(r.key,r.arg):i=null}var r,i;this._invoke=function(e,n){return new Promise(function(t,a){var s={key:e,arg:n,resolve:t,reject:a,next:null};i?i=i.next=s:(r=i=s,o(e,n))})},"function"!=typeof n.return&&(this.return=void 0)}"function"==typeof Symbol&&Symbol.asyncIterator&&(n.prototype[Symbol.asyncIterator]=function(){return this}),n.prototype.next=function(e){return this._invoke("next",e)},n.prototype.throw=function(e){return this._invoke("throw",e)},n.prototype.return=function(e){return this._invoke("return",e)}}();var n=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var o=arguments[n];for(var t in o)Object.prototype.hasOwnProperty.call(o,t)&&(e[t]=o[t])}return e},o={breakpoint:null,oldLocation:null,newLocation:null,onMove:null,movedClass:"moved",methods:{o:"appendTo",n:"appendTo"}},t=function(e){return"appendTo"===e||"prependTo"===e||"insertBefore"===e||"insertAfter"===e?1:0},r=function(e){return e?(console.error("[Move]: "+e),1):0};e.fn.move=function(i){var a=n({},o,i);if(!t(a.methods.o))return r("Please enter a valid method for scaling up. Valid properties are: appendTo, prependTo, insertBefore, and insertAfter."),0;if(!t(a.methods.n))return r("Please enter a valid method for scaling down. Valid properties are: appendTo, prependTo, insertBefore, and insertAfter."),0;var s=function(e){var n=window.innerWidth?window.innerWidth:document.documentElement.clientWidth,o=void 0;e.length&&(n<=a.breakpoint&&!e.hasClass(a.movedClass)&&(o=a.methods.n,e.addClass(a.movedClass),e[o](a.newLocation),null!==a.onMove&&a.onMove("newLocation",e)),n>a.breakpoint&&e.hasClass(a.movedClass)&&(o=a.methods.o,e.removeClass(a.movedClass),e[o](a.oldLocation),null!==a.onMove&&a.onMove("oldLocation",e)))};return e(this).each(function(){var n=e(this);s(n),setTimeout(function(){return s(n)},300),e(window).resize(function(){return s(n)})})}}($);
