.form {

    &.form--payment {

        .gform_wrapper {

            .gform_body {
                border-bottom: 1px solid $midlightgrey;
            }

            .gsection {
                display: none;

                &.active {
                    display: block;
                }
            }

            .gfmc-column {
                display: none;

                &.active {
                    display: block;
                }
            }

            .gform_fields {
                > li {
                    &:first-child, &:nth-child(2) {
                        display: block!important;
                    }
                }
            }

        }
    }

    &__inner {
        padding: 60px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 45px 0;
        }
    }

    &__section-header {
        padding: 25px;
        border: 1px solid $midlightgrey;
        background: $offwhitethree;
    }

    &__section-title {
        margin: 0;
        font-size: 3rem;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 2rem;
        }
    }

    &__section-content {
        padding: 25px;
        border: 1px solid $midlightgrey;
        border-top: none;
        border-bottom: none;
    }

    &__back-link-wrapper {
        padding-bottom: 30px;

        a {
            text-decoration: none;
            font-weight: 400;
            color: $textgrey;

            &:hover, &:focus {
                text-decoration: none;
                color: $teal;
            }

        }
    }

    &__details {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -10px;
    }

    &__detail {
        flex: 1 0;
        padding: 0 10px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            flex: 0 0 100%;
            max-width: 100%;
            padding-top: 15px;
        }


        &:first-child {
            flex: 0 0 auto;
        }

        select {
            height: 30px;
            padding: 0 30px 0 0;
            border: none;
            text-decoration: underline;
            font-size: 1.6rem;
            font-family: $bodyfontfamily;
            font-weight: 600;
            color: $teal;
            cursor: pointer;
        }

        input {
            display: inline-block;
            width: 110px;
            min-width: 0;
            height: 30px;
            padding: 0 30px 0 0;
            border: none;
            text-decoration: underline;
            font-size: 1.6rem;
            font-family: $bodyfontfamily;
            font-weight: 600;
            color: $teal;
        }
    }

    &__detail-heading,
    &__detail-label {
        display: block;
        color: $bluegrey;
    }

    &__thumbnail {
        max-width: 140px;
    }

    &__heading-wrapper {
        margin-bottom: 30px;
        text-align: center;

        .gform_title {
            margin-bottom: 5px;
            font-size: 4.5rem;
        }
    }

    .gform_heading {
        margin-bottom: 30px;
        text-align: center;

        .gform_title {
            font-size: 4.5rem;
        }
    }

    &__next {
        padding-top: 20px;
        text-align: right;

        .button {
            cursor: pointer;
        }
    }

    .validation_error {
        padding: 20px 0 20px;
        margin: 0;
        border-top: 1px solid $midlightgrey;
        border-left: 1px solid $midlightgrey;
        border-right: 1px solid $midlightgrey;
    }

    .gfmc-column {
        display: none;

        &.active {
            display: block;
        }

        &:nth-last-child(2), &:last-child {
            &.active {
                border-bottom: 1px solid $midlightgrey;
            }

            .form__next {
                display: none;
            }
        }

        .gform_footer {
            text-align: right;

            input {
                margin-right: 0;
                border-color: $teal;
            }
        }
    }

    .gform_confirmation_wrapper {
        padding: 80px 0;
    }

    .gform_wrapper {
        .gsection {

            &.section-payment {
                + .gfmc-column {
                    .form__next {
                        display: none;
                    }
                }

            }

            .gsection_title {
                color: #b5b5b5;
                background: #f0f0f0;
            }

            &.active {
                .gsection_title {
                    color: $bluegrey;
                    background: $offwhitethree;
                }
            }
        }

        .gfield_required {
            display: none;
        }

        input,
        textarea,
        select {
            border-radius: 4px;
            border: 1px solid $midlightgrey;

            &:focus {
                border: 2px solid $teal;
            }
        }

        input, select {
            height: 50px;
        }

        .gfield {
            display: flex;
            flex-wrap: wrap;
        }

        .gform_validation_container {
            display: none;
        }

        .gfield_label {
            display: block;
            margin: 0;
            padding: 10px 20px 0 0;
            flex: 0 0 40%;
            max-width: 40%;
            text-align: right;

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                flex: 0 0 100%;
                max-width: 100%;
                text-align: left;
            }
        }

        .ginput_container {
            flex: 0 0 60%;
            max-width: 60%;

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }
    }

    .gfield_creditcard_warning_message {
        display: block;
        width: 100%;
        padding: 10px 0 30px;
        text-align: center;
    }

    .ginput_container_creditcard {

        .ginput_full {
            display: block;
            position: relative;
            margin-bottom: 15px;

        }

        .ginput_card_security_code_icon {
            display: none;
        }

        .ginput_card_expiration_container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        select {
            display: inline-block;
            flex: 0 0 48%;
            max-width: 48%;
            background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%2301a59f'><polygon points='0,0 100,0 50,50'/></svg>");
            background-size: 12px;
            background-position: calc(100% - 10px) calc(50% + 4px);
            background-repeat: no-repeat;
            margin-bottom: 5px;

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }

        .gform_card_icon_container {
            position: absolute;
            right: 14px;
            top: 10px;

            + input + label {
                display: none;
            }

            .gform_card_icon {
                display: none;
                background-image: url("../img/credit-cards-sprite.png");
                width: 36px;
                height: 32px;
                color: transparent;
                font-size: 0;

                &_selected {
                    display: block;
                }

                &_amex {
                    background-position: -72px 0;
                }

                &_discover {
                    background-position: -108px 0;
                }

                &_mastercard {
                    background-position: -36px 0;
                }

                &_visa {
                    background-position: 0 0;
                }

                &_jcb {
                    background-position: -180px 0;
                }

                &_maestro {
                    background-position: -144px 0;
                }
            }
        }
    }

    &__footer-copy {
        a {
            text-decoration: none;
        }
    }
}


.payment-details {
    flex: 0 0 100%;
    max-width: 100%;

    &__link-wrapper {
        text-align: right;

        a {
            font-weight: 400;
            color: $textgrey;

            &:hover, &:focus {
                color: $teal;
            }
        }
    }



    &__notice {
        display: none;
        padding: 20px;
        margin-top: 30px;
        background: $offwhitethree;
        
        &.active {
            display: block;
        }

        a {
            text-decoration: none;
        }

        p {
            margin: 0;
        }

        svg {
            color: $fail;
        }
    }
}