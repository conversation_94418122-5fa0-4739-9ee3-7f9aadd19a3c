.image-content-columns {
    &__inner {
        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 30px 0;
        }
    }

    &__row {
        align-items: center;
        padding: 70px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 30px 0;
        }

        &:nth-child(odd) {
            flex-direction: row-reverse;

            .image-content-columns__content {
                margin-left: auto;

                @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                    margin: 0;
                }
            }
        }
    }

    &__col {
        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            &:first-child {
                order: 2;
            }

            &:last-child {
                order: 1;
            }
        }
    }

    &__content {
        max-width: 430px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding-top: 30px;
            max-width: 100%;
        }
    }

    &__heading {
        margin-bottom: 15px;

        a {
            font-weight: 400;
            text-decoration: none;
            color: $bluegrey;

            &:hover, &:focus {
                color: $teal;
            }
        }
    }

    &__button-wrapper {
        padding-top: 10px;
    }
}
