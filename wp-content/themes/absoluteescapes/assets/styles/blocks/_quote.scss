.quote {
    &__inner {
        padding: 40px 0;
    }

    blockquote {
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p,
        li {
            font-size: 6rem;

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                font-size: 3.6rem;
            }
        }
    }

    &__label {
        font-size: 2.9rem;
        color: $bluegrey;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 2rem;
        }
    }

    .review-bar {
        display: none;
        position: static;
        left: 0;
        z-index: 50;
        width: 100%;
        padding: 15px 0 45px;
        text-align: center;
        background: rgba($white, 0.4);

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            display: block;
        }
    }
}
