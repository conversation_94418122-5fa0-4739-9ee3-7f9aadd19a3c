.reviews {
    position: relative;
    overflow: hidden;

    &__inner {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding: 50px 0 140px;
        background: $offwhite;

        &:after {
            content: '';
            display: block;
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 90px;
            background: url(../img/banner-mask.svg) center top no-repeat;
            background-size: 100% 100%;


            @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                height: 60px;
            }

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                height: 45px;
            }

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                height: 30px;
            }
        }
    }

    &__row {
        padding: 45px 45px 0;
        text-align: center;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 45px 60px 0;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 45px 0 0; 
        }
    }

    &__col {
        width: 33.333333%;
        padding: 0 30px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            width: 50%;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 0 15px;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            width: 100%;
        }
    }

    &__copy {
        p {
            display: inline;
        }
    }

    &__rating {
        margin-bottom: 10px;
        color: $pink;
    }

    &__rating-star {
        font-size: 1.6rem;
    }

    &__review-copy {
        p,li {
            line-height: 1.5;
        }
    }

    &__graphic {
        text-align: center;
        padding-top: 60px;
    }

    .flickity-page-dots {
        display: none;
        bottom: -40px;


        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            display: block;
        }

        .dot {
            background: $bluegrey;
        }
    }
}