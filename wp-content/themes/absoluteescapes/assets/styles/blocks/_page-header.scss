.page-header {

    &--taxonomy {
        .page-header__content {
            padding: 220px 0 260px;

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                padding: 120px 0 80px;
            }
        }

        .page-header__background {
            &:after {
                content: '';
                display: block;
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 60px;
                background: url(../img/banner-mask.svg) center top no-repeat;
                background-size: 100% 100%;

                @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                    height: 30px;
                }
            }
        }
    }

    &__background {
        position: relative;
        height: 100%;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        overflow: hidden;
    }

    &__inner {
        position: relative;
        height: 100%;

        &:after {
            content: '';
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 30px;
            left: 0;
            z-index: 5;
            width: 100%;
            height: 0;
            margin: auto 0;
            box-shadow: 0 0 100px 80px rgba($black, 0.55);
        }
    }

    &__content {
        position: relative;
        z-index: 7;
        padding: 180px 0 120px;


        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 100px 0 80px;
        }
    }

    &--page {
        .page-header__alt {
            display: none !important;
        }
    }

    &__heading {
        margin-bottom: 5px;
    }

    &__subheading {
        padding-top: 10px;
        margin-bottom: 15px;
    }


    &__buttons {
        padding-top: 15px;
        .page-header__button-wrapper {
            margin: 0 7px;
        }
    }

    &__button-wrapper {
        padding-top: 15px;
    }


    &__alt {
        display: inline-block;
        position: absolute;
        bottom: 0;
        left: 0;
        padding: 10px;
        background: rgba($black, 0.5);
        color: $white;
    }
}
