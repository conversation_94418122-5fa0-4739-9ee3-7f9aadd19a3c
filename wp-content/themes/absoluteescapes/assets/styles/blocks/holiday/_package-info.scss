.package-info {

    &__inner {
        position: relative;
        z-index: 2;
        padding: 65px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 45px 0;
        }
    }


    &__container {
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 20px;
        }
    }

    &__items {
        padding-left: 0;
        margin-bottom: 30px;
        list-style: none;
    }

    li {
        margin-bottom: 10px;
    }

}
