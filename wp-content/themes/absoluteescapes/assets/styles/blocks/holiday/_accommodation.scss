.accommodation {
    position: relative;
    //background: $offwhitethree;

    &:after {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: -500%;
        z-index: 0;
        width: 1000%;
        height: 100%;
        // background: inherit;
    }

    .button {
        background: $blue;
        color: $white;

        &:hover, &:focus {
            background: $teal;
        }
    }

    &__inner {
        position: relative;
        z-index: 2;
        padding: 65px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 45px 0;
        }

    }

    &__container {

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 20px;
        }
    }

    .accommodation__container--table {
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            width: 100%;
            max-width: 100%;
            padding: 0;
        }
    }

    &__images {
        max-width: 800px;
        padding: 20px 0 40px;
    }

    &__image {
        width: 100%;
        position:relative;
        span.alt-caption{
            position: absolute;
            display: inline-block;
            padding: 4px 8px;
            border-radius: 5px;
            background: hsla(0,0%,100%,.3);
            color: #fff;
            bottom: 20px;
            right: 20px;
            font-size: 14px;

            &.alt-caption--dark {
                background: rgba(0,0,0, .2);
                color: $black;
            }
        }
    }

    .flickity-button {
        border-color: $blue;
        background: $blue;
        color: $white;
        box-shadow: 0 0 25px rgba($black, 0.35);

        &:hover, &:focus {
            border-color: $teal;
            background: $teal;
            color: $white;
        }

        &:disabled {
            opacity: 0;
        }

    }
    .flickity-page-dots {
        bottom: 55px;
    }

    .itineraries-prices__table {
        padding: 30px 0;
    }

    .itineraries-prices__expanded-col {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .accordion {
        margin: 0;

        &__item {
            flex: 0 0 100%;
            max-width: 100%;
            padding: 0;
            border-top: 1px solid $midlightgrey;
            transition: 300ms;

            &:after {
                display: none;
            }

            &.highlight:not(.active){
                background-color:#efefef;

                .accordion__heading-wrapper *{
                    color:$black;
                }
            }
            &.active {
                border-color: $teal;
                background: $teal;

                .accordion__heading-wrapper {
                    .itineraries-prices__table-row {
                        span {
                            color: $white;
                        }
                    }
                    svg {
                        transform: rotate(180deg);
                        color: $white;
                    }
                }
            }
        }

        &__heading-wrapper {
            position: relative;
            *{
                font-weight: bold;
            }
            svg {
                position: absolute;
                top: 0;
                right: 20px;
                bottom: 0;
                margin: auto 0;
                color: $bluegrey;
                transition: 300ms;

                @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                    right: 15px;
                }

                @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                    font-size: 1rem;
                }

                @media only screen and (max-width: 360px) {
                    right: 5px;
                }
            }
        }
    }

    .package-info__lists {
        padding: 30px 0;
    }

    &__information {
        border: 1px solid $midlightgrey;
        background: $white;

    }

    &__information-content {
        padding: 30px;


        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 20px;
        }
    }

    li {
        margin-bottom: 10px;
    }
}
