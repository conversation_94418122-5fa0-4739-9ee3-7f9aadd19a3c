.car-hire {
    &__inner {
        position: relative;
        z-index: 2;
        padding: 65px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 45px 0;
        }
    }

    &__container {

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 20px;
        }
    }

    li {
        margin: 0;
    }

    &__info-box {
        padding: 40px 30px;
        margin: 30px 0;
        border: 1px solid $midlightgrey;
        background: #efefef;

        ul {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        li {
            position: relative;
            &:before {
                content: "";
                display: inline-block;

                width: 16px;
                height: 11px;
                margin: auto 8px auto 0;
                background: url(../img/arrow.svg) center no-repeat;
                background-size: contain;
            }
        }
    }

    &__copy, &__info-copy {

        ul {
            padding: 0;
        }

        li {
            display: block;
            position: relative;
            padding-left: 1.5em;
            text-indent: -1.5em;

            &:before {
                content: "";
                display: inline-block;
                width: 16px;
                height: 11px;
                margin: auto 8px auto 0;
                background: url(../img/arrow.svg) center no-repeat;
                background-size: contain;
            }

        }
    }

    &__info-copy {
        padding: 20px 0;
    }

    &__options {
        padding-bottom: 30px;


    }

    &__option {
        padding: 30px;
        margin: 20px 0;
        border: 1px solid $midlightgrey;
        background: #efefef;

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 20px;
        }

        ul {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        li {
            position: relative;
            &:before {
                content: "";
                display: inline-block;

                width: 16px;
                height: 11px;
                margin: auto 8px auto 0;
                background: url(../img/arrow.svg) center no-repeat;
                background-size: contain;
            }
        }

        .car-hire__heading {
            margin-bottom: 0;
        }
    }

    &__text {
        color: $bluegrey;
    }

    &__option-row {
        margin: 0 -15px;
    }

    &__option-col {
        flex: 0 0 50%;
        max-width: 50%;
        padding: 0 15px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            flex: 0 0 100%;
            max-width: 100%;

            &:last-child {
                padding-top: 5px;
            }
        }
    }

}
