.itineraries-prices {
    position: relative;
    //background: #efefef;

    &:after {
        content: "";
        display: block;
        position: absolute;
        top: 0;
        left: -500%;
        z-index: 0;
        width: 1000%;
        height: 100%;
        // background: inherit;
    }

    &__inner {
        position: relative;
        z-index: 2;
        padding: 65px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 45px 0;
        }
    }

    &__container {
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 20px;
        }
    }

    .itineraries-prices__container--table {
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            width: 100%;
            max-width: 100%;
            padding: 0;
        }
    }

    .accordion {
        margin: 0;

        &__item {
            flex: 0 0 100%;
            max-width: 100%;
            padding: 0;
            border-top: 1px solid $midlightgrey;
            transition: 300ms;

            &:after {
                display: none;
            }

            &.active {
                border-color: $teal;
                background: $teal;


                .accordion__heading-wrapper {
                    .itineraries-prices__table-row {
                        *
                        span {
                            color: $white;
                        }

                        span.itineraries-prices__difficulty-icon {
                            color: $bluegrey;
                        }
                    }

                    .fa-chevron-down {
                        transform: rotate(180deg);
                        color: $white;
                    }
                }
            }
        }

        &__heading-wrapper {
            position: relative;

            .fa-chevron-down {
                position: absolute;
                top: 0;
                right: 20px;
                bottom: 0;
                margin: auto 0;
                color: $bluegrey;
                transition: 300ms;

                @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                    right: 15px;
                }

                @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                    font-size: 1.2rem;
                }

                @media only screen and (max-width: 360px) {
                    right: 8px;
                }
            }
        }
    }

    &__table-row,
    &__prices-row {
        display: flex;
        flex-wrap: wrap;
        padding: 15px 0;
        font-size: 1.8rem;

        color: $bluegrey;


        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 1.3rem;
        }

        &--heading {
            span {
                font-weight: 600;
                color: $teal;
            }
        }
    }

    &__table-row {
        padding-right: 30px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 1.6rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 10px 10px;
        }
    }

    &__table-col,
    &__prices-col {
        flex: 1 0;
        padding: 0 10px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 0 8px;
            font-size: 1.3rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 5px;
            font-size: 1.3rem;
        }
    }

    &__table-col {

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {

            font-size: 1.6rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            font-size: 1.6rem;
        }

        &--desktop {
            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                display: none;
            }
        }

        &--difficulty-mobile {
            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                display: block;
                flex: 0 0 auto;
                min-width: 120px; // Fixed width for consistent alignment
            }
        }

        &--mobile {
            display: none;
            padding-top: 15px;
            padding-bottom: 15px;
            border-top: 1px solid $white;

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                padding-right: 15px;
                padding-left: 15px;
            }

            span {

                color: $white;

                &.itineraries-prices__difficulty-icon {
                    color: $bluegrey;
                }
            }


            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                display: block;
                flex: 0 0 100%;
                max-width: 100%;
            }
        }
    }

    &__table-col-item {
        display: block;
        margin-bottom: 3px;
    }

    &__expanded {
        position: relative;
        padding: 40px 35px;
        border: 1px solid $midlightgrey;
        background: $white;

        @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
            padding: 45px 25px 35px;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 45px 20px 35px;
        }
    }

    &__expanded-col {
        flex: 0 0 50%;
        max-width: 50%;
        padding: 0 15px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
            flex: 0 0 100%;
            max-width: 100%;
        }


        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            flex: 0 0 50%;
            max-width: 50%;
        }


        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            flex: 0 0 100%;
            max-width: 100%;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0
        }
    }

    &__number-wrapper {
        margin-right: 20px;
    }

    &__number {
        display: block;
        position: relative;
        width: 26px;
        height: 26px;
        line-height: 26px;
        border-radius: 50%;
        text-align: center;
        font-weight: bold;
        background: $blue;
        color: $white;

        &:after {
            content: "";
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            width: 1px;
            height: 1000%;
            margin: 0 auto;
            background: $midlightgrey;
            transform: translateY(10%);
        }


    }

    &__points {
        max-width: 250px;
        margin: 0 auto;
    }

    &__point {
        display: flex;
        flex-wrap: wrap;
        padding-bottom: 15px;
        overflow: hidden;

        &:first-child {
            .itineraries-prices__number {
                border: 3px solid $blue;
                background: $white;
            }
        }

        &:last-child {
            .itineraries-prices__number {
                &:after {
                    display: none;
                }

                border: 3px solid $blue;
                background: $white;
            }
        }
    }

    &__text-wrapper {
        flex: 1 0;
    }

    .itineraries-prices__prices {
        padding-top: 15px;

        .accordion__item {
            &:last-child {
                border-bottom: 1px solid $midlightgrey;
            }

            .accordion__heading-wrapper {
                .fa-chevron-down {
                    transform: rotate(0);
                    color: $teal;
                }
            }

            &.active {
                .itineraries-prices__prices-col {
                    span {
                        color: $white;
                    }
                }

                .accordion__heading-wrapper {
                    .fa-chevron-down {
                        transform: rotate(180deg);
                        color: $white;
                    }
                }
            }
        }

        .accordion__copy {
            padding: 30px 15px;
            background: $white;

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                padding: 25px 0;
            }

            &:after {
                content: '';
                display: table;
                clear: both;
            }

            .accordion__button-wrapper {
                text-align: right;
            }

            .button {
                background: $blue;
                color: $white;

                &:hover, &:focus {
                    background: $teal;
                }
            }
        }
    }

    .itineraries-prices__prices-row {
        + .fa-chevron-down {
            color: $teal;
        }

        &--offwhite {
            background: $offwhitethree;
        }
    }

    &__prices-col {
        &:first-child {
            flex: 0 0 calc(25% - 10px); // Reduce first column by 10px

            span {
                color: $teal;
            }
        }

        &:nth-child(2) {
            flex: 0 0 calc(25% + 7.5px); // Add half of savings (15px / 2)
        }

        &:nth-child(3) {
            flex: 0 0 calc(25% + 7.5px); // Add half of savings (15px / 2)
        }

        &--button {
            flex: 0 0 calc(25% - 20px); // Reduce button column by 20px

            .button {
                width: 100%;
                min-width: auto;
            }
        }

        &--arrow {
            flex: 0 0 40px; // Set arrow column to 40px
            max-width: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        &:last-child {
            span {
                font-weight: bold;
            }
        }

        span {
            font-weight: 600;
            color: $bluegrey;
        }
    }

    &__copy {
        &--footer {
            padding-top: 45px;
            text-align: right;

            p,
            li {
                font-size: 1.8rem;
                color: $bluegrey;


                @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                    font-size: 1.6rem;
                }
            }
        }
    }

    &__difficulty-wrapper {
        display: inline-block;
        vertical-align: top;
    }

    &__difficulty {
        display: block;
        font-size: 1.4rem;
        text-align: center;
        line-height: 1;

        // Override for mobile difficulty column
        .itineraries-prices__table-col--difficulty-mobile & {
            display: inline-block !important;
            text-align: left !important;
            font-size: 1.1rem !important;
            vertical-align: middle !important;
        }
    }

    &__difficulty-score {
        display: flex;
        text-align: center;
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
        gap: 2px;
        // Remove max-width constraint to allow horizontal layout

        // Try to achieve 2x2 layout when there are 4 icons
        .itineraries-prices__difficulty-icon {
            flex: 0 0 calc(50% - 1px); // Two icons per row with gap
            max-width: calc(50% - 1px);
        }

        // Override for mobile difficulty column
        .itineraries-prices__table-col--difficulty-mobile & {
            display: inline-flex !important;
            text-align: left !important;
            vertical-align: middle !important;
            justify-content: center !important;
        }

        // For smaller screens, allow 1x4 layout if needed
        @media only screen and (max-width: 767px) {
            max-width: none;

            .itineraries-prices__difficulty-icon {
                flex: 0 0 auto;
                max-width: none;
            }
        }
    }

    &__difficulty-icon {
        display: inline-block;
        position: relative;
        width: 28px;
        height: 28px;
        font-size: 1.2rem;
        border-radius: 50%;
        border: 1px solid $bluegrey;
        background: $white;

        svg {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            margin: auto;
        }

        i {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        // Mobile specific sizing and centering
        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            display: inline-flex;
            align-items: center;
            justify-content: center;

            i {
                position: static;
                transform: none;
            }
        }
    }

    // Responsive breakpoint styling

    // Smallest screens (below 768px): 3 visible columns + arrow
    @media only screen and (max-width: 767px) {
        &__table-row,
        &__prices-row {
            padding-right: 35px; // Reserve space for down arrow
        }

        // Make the 3 visible columns equal width (excluding arrow)
        &__table-col:not(&__table-col--arrow),
        &__prices-col:not(&__prices-col--arrow) {
            flex: 1 1 0; // Equal flex-grow, equal width distribution
            min-width: 0; // Allow shrinking
            text-align: left !important;
        }

        &__table-col--arrow,
        &__prices-col--arrow {
            flex: 0 0 40px !important; // Fixed 40px width for arrow column
            max-width: 40px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;

            .fa-chevron-down {
                position: static !important;
                margin: 0 !important;
            }
        }

        // Left-align all column headers to content
        &__table-row--heading {
            .itineraries-prices__table-col,
            .itineraries-prices__table-col--difficulty-mobile,
            .itineraries-prices__table-col--price {
                text-align: left !important;
                align-items: flex-start !important;
            }
        }

        // Price header - show mobile text only
        .itineraries-prices__price-text-desktop {
            display: none !important;
        }

        .itineraries-prices__price-text-mobile {
            display: inline !important;
        }

        // Difficulty column - horizontal series layout for mobile too
        &__table-col--difficulty-mobile {
            .itineraries-prices__difficulty-wrapper {
                display: flex;
                flex-direction: column;
                align-items: flex-start; // Left-align the entire group
                text-align: left !important;
                width: fit-content; // Wrapper should be fit-content

                .itineraries-prices__difficulty-score {
                    display: flex; // Horizontal series layout for mobile
                    flex-direction: row;
                    flex-wrap: wrap; // Allow wrapping as needed
                    justify-content: flex-start;
                    align-items: center;
                    gap: 2px;
                    width: fit-content;
                    margin-bottom: 4px;

                    .itineraries-prices__difficulty-icon {
                        // Keep existing icon styles
                        margin: 0;
                    }
                }

                .itineraries-prices__difficulty {
                    text-align: center !important;
                    font-size: 0.9rem;
                    line-height: 1.2;
                    margin-top: 2px;
                    align-self: flex-start;
                    width: 100%;
                }
            }
        }

        // Make 'i' icon inline after days/nights text in Highlights section
        &__itinerary-main {
            .tooltip {
                display: inline !important;

                img {
                    margin-left: 3px !important;
                }
            }
        }
    }

    // 768px and above: 5 visible columns + arrow (retain small screen settings but show more columns)
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        &__table-col,
        &__prices-col {
            flex: 1 1 0; // Equal width distribution
            min-width: 0;
            text-align: left !important;
        }

        // Give difficulty column more space, price column less space
        &__table-col--difficulty-mobile {
            flex: 1.25 1 0; // Balanced space for difficulty icons
        }

        &__table-col--price,
        &__prices-col--price {
            flex: 1.0 1 0; // More space for price column
            min-width: 80px; // Minimum width for "Price / person"
        }

        // Pricing column specific widths
        &__prices-col {
            &:first-child {
                flex: 0 0 calc(25% - 10px); // Reduce first column by 10px
            }

            &:nth-child(2) {
                flex: 0 0 calc(25% + 7.5px); // Add half of savings
            }

            &:nth-child(3) {
                flex: 0 0 calc(25% + 7.5px); // Add half of savings
            }

            &--button {
                flex: 0 0 calc(25% - 20px); // Reduce button column by 20px
            }
        }

        &__table-col--arrow,
        &__prices-col--arrow {
            flex: 0 0 40px !important;
            max-width: 40px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;

            .fa-chevron-down {
                position: static !important;
                margin: 0 !important;
            }
        }

        // Price header - show desktop text only
        .itineraries-prices__price-text-desktop {
            display: inline !important;
        }

        .itineraries-prices__price-text-mobile {
            display: none !important;
        }

        // Left-align all column headers to content and allow wrapping
        &__table-row--heading {
            .itineraries-prices__table-col,
            .itineraries-prices__table-col--difficulty-mobile,
            .itineraries-prices__table-col--price {
                text-align: left !important;
                align-items: flex-start !important;
                white-space: normal; // Allow text wrapping
                overflow: visible;
            }
        }

        // Difficulty column - horizontal series layout that can wrap
        &__table-col--difficulty-mobile {
            .itineraries-prices__difficulty-wrapper {
                display: flex;
                flex-direction: column;
                align-items: flex-start; // Left-align the entire group
                text-align: left !important;
                width: fit-content; // Wrapper should be fit-content

                .itineraries-prices__difficulty-score {
                    display: flex !important; // Override mobile grid layout
                    flex-direction: row !important; // Horizontal series layout
                    flex-wrap: wrap; // Allow wrapping as needed
                    justify-content: flex-start;
                    align-items: center;
                    gap: 2px;
                    width: fit-content;
                    margin-bottom: 4px;
                    grid-template-columns: none !important; // Remove grid layout

                    .itineraries-prices__difficulty-icon {
                        // Override the 50% width constraint for horizontal layout
                        flex: 0 0 auto !important; // Allow natural icon width
                        max-width: none !important; // Remove width constraint
                        margin: 0;
                    }
                }

                .itineraries-prices__difficulty {
                    text-align: center !important;
                    font-size: 0.9rem;
                    line-height: 1.2;
                    margin-top: 2px;
                    align-self: flex-start;
                    width: 100%;
                }
            }
        }

        // Make 'i' icon inline after days/nights text in Highlights section
        &__itinerary-main {
            .tooltip {
                display: inline !important;

                img {
                    margin-left: 3px !important;
                }
            }
        }
    }

    // 992px and above: 4 visible columns + arrow (retain settings but different column visibility)
    @media only screen and (min-width: 992px) {
        // Default table column styles
        &__table-col,
        &__prices-col {
            flex: 1 1 0; // Equal flex-grow for consistent width distribution
            min-width: 0; // Allow shrinking
            text-align: left !important;
        }

        // Specific column widths for table container
        .itineraries-prices__container--table & {
            &__table-row {
                padding-right: 0px;
            }

            &__table-col {
                &:nth-child(1) { // Tour code
                    flex: 0 0 13%;
                }

                &:nth-child(2) { // Days / Nights
                    flex: 0 0 20%;
                }

                &:nth-child(3) { // Average miles
                    flex: 0 0 23%;
                }

                &--difficulty-mobile { // Difficulty
                    flex: 1.25 1 0;
                }

                &--price { // Price per person
                    flex: 1.25 1 0;
                    min-width: 80px;
                }

                &--arrow { // Arrow
                    flex: 0 0 40px;
                    max-width: 40px;
                }
            }
        }

        // Give difficulty column more space, price column less space (for non-table containers)
        &__table-col--difficulty-mobile {
            flex: 1.25 1 0; // Balanced space for difficulty icons
        }

        &__table-col--price,
        &__prices-col--price {
            flex: 1.25 1 0; // Equal space for price column
            min-width: 80px; // Minimum width for "Price per person"
        }

        // Pricing column specific widths
        &__prices-col {
            &:first-child {
                flex: 0 0 calc(25% - 10px); // Reduce first column by 10px
            }

            &:nth-child(2) {
                flex: 0 0 calc(25% + 7.5px); // Add half of savings
            }

            &:nth-child(3) {
                flex: 0 0 calc(25% + 7.5px); // Add half of savings
            }

            &--button {
                flex: 0 0 calc(25% - 20px); // Reduce button column by 20px
            }
        }

        &__table-col--arrow,
        &__prices-col--arrow {
            flex: 0 0 40px !important;
            max-width: 40px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;

            .fa-chevron-down {
                position: static !important;
                margin: 0 !important;
            }
        }

        // Price header - show desktop text only
        .itineraries-prices__price-text-desktop {
            display: inline !important;
        }

        .itineraries-prices__price-text-mobile {
            display: none !important;
        }

        // Left-align all column headers to content and allow wrapping
        &__table-row--heading {
            .itineraries-prices__table-col,
            .itineraries-prices__table-col--difficulty-mobile,
            .itineraries-prices__table-col--price {
                text-align: left !important;
                align-items: flex-start !important;
                white-space: normal; // Allow text wrapping instead of ellipsis
                overflow: visible;
            }
        }

        // Difficulty column - horizontal series layout that can wrap
        &__table-col--difficulty-mobile {
            .itineraries-prices__difficulty-wrapper {
                display: flex;
                flex-direction: column;
                align-items: flex-start; // Left-align the entire group
                text-align: left !important;
                width: fit-content; // Wrapper should be fit-content

                .itineraries-prices__difficulty-score {
                    display: flex !important; // Override mobile grid layout
                    flex-direction: row !important; // Horizontal series layout
                    flex-wrap: wrap; // Allow wrapping as needed
                    justify-content: flex-start;
                    align-items: center;
                    gap: 2px;
                    width: fit-content;
                    margin-bottom: 4px;
                    grid-template-columns: none !important; // Remove grid layout

                    .itineraries-prices__difficulty-icon {
                        // Override the 50% width constraint for horizontal layout
                        flex: 0 0 auto !important; // Allow natural icon width
                        max-width: none !important; // Remove width constraint
                        margin: 0;
                    }
                }

                .itineraries-prices__difficulty {
                    text-align: center !important;
                    font-size: 0.9rem;
                    line-height: 1.2;
                    margin-top: 2px;
                    align-self: flex-start;
                    width: 100%;
                }
            }
        }

        // Make 'i' icon inline after days/nights text in Highlights section
        &__itinerary-main {
            .tooltip {
                display: inline !important;

                img {
                    margin-left: 3px !important;
                }
            }
        }
    }

    // New itinerary styling
    &__itinerary-main {
        .fa-sun {
            color: #ffa500; // Orange for sun
            margin: 0 2px;
        }

        .fa-moon {
            color: #4169e1; // Royal blue for moon
            margin: 0 2px;
        }
    }

    // Days/nights display control - always show full format
    &__days-nights-full {
        display: inline !important; // Always show full version (5 days / 6 nights)
        // Allow wrapping at forward slash but keep number+word together
        white-space: normal;
        word-break: keep-all; // Prevent breaking within words
    }











}
