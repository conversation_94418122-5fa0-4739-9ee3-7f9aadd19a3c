.we-recommend {

    &__inner {
        padding: 100px 0 120px;
        border-top: 8px solid $bluegrey;
        border-bottom: 8px solid $bluegrey;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 60px 0;
        }
    }

    &__heading-wrapper {
        padding-bottom: 35px;
    }

    &__content {
        max-width: 375px;
    }

    &__title {
        padding-top: 8px;
        margin-bottom: 2px;
    }

    &__price {
        h3 {
            margin-bottom: 10px;
        }
    }

    &__button-wrapper {
        padding-top: 10px;
    }

    &__image {
        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding-top: 30px;
        }
    }
}