@font-face {
  font-family: "VAG Rounded";
  src: url("../fonts/VAGRounded-Light.eot");
  src: url("../fonts/VAGRounded-Light.eot?#iefix") format("embedded-opentype"),
    url("../fonts/VAGRounded-Light.woff2") format("woff2"),
    url("../fonts/VAGRounded-Light.woff") format("woff"),
    url("../fonts/VAGRounded-Light.ttf") format("truetype"),
    url("../fonts/VAGRounded-Light.svg#VAGRounded-Light") format("svg");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "VAG Rounded";
  src: url("../fonts/VAG-Rounded-Bold.eot");
  src: url("../fonts/VAG-Rounded-Bold.eot?#iefix") format("embedded-opentype"),
    url("../fonts/VAG-Rounded-Bold.woff2") format("woff2"),
    url("../fonts/VAG-Rounded-Bold.woff") format("woff"),
    url("../fonts/VAG-Rounded-Bold.ttf") format("truetype"),
    url("../fonts/VAG-Rounded-Bold.svg#VAG-Rounded-Bold") format("svg");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: "VAG Rounded";
  src: url("../fonts/VAGRoundedStd-Thin.eot");
  src: url("../fonts/VAGRoundedStd-Thin.eot?#iefix") format("embedded-opentype"),
    url("../fonts/VAGRoundedStd-Thin.woff2") format("woff2"),
    url("../fonts/VAGRoundedStd-Thin.woff") format("woff"),
    url("../fonts/VAGRoundedStd-Thin.ttf") format("truetype"),
    url("../fonts/VAGRoundedStd-Thin.svg#VAGRoundedStd-Thin") format("svg");
  font-weight: 300;
  font-style: normal;
}
