.breadcrumbs {
    &__inner {
        position: relative;
        padding: 25px 0 65px;
        background: $bluegrey;

        &:after {
            content: "";
            display: block;
            position: absolute;
            bottom: 0;
            left: 0;
            z-index: 1;
            width: 100%;
            height: 45px;
            background: url(../img/banner-mask.svg) center top no-repeat;
            background-size: 100% 100%;

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                height: 30px;
            }
        }
    }

    p {
        font-size: 1.9rem;
        margin: 0;
    }

    a {
        font-family: $headingfontfamily;
        font-weight: 400;
        margin: 0 10px;
        color: $white;
        text-decoration: none;
        &:hover,
        &:focus {
            color: $teal;
        }
    }

    #breadcrumbs {
        > span {
            > span {
                > a {
                    margin-left: 0;
                }
            }
        }
    }

    span {
        font-family: $headinglightsfontfamily;
        font-weight: 500;
        color: $white;
    }

    .breadcrumb_last {
        margin-left: 10px;
    }
}
