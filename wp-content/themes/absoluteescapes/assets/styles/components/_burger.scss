/* Burger Menu
============== */

.masthead__burger {
    &.active {
        .masthead__burger-lines {
            background: rgba($white, 0);

            &:after {
                transform: rotate(-45deg);
                -webkit-transform: rotate(-45deg);
                bottom: 0;
            }

            &:before {
                transform: rotate(45deg);
                -webkit-transform: rotate(45deg);
                top: 0;
            }
        }
    }
    .masthead__burger-wrapper {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        margin-bottom: 14px;
    }

    .masthead__burger-menu {
        display: block;
        position: relative;
        top: 7px;
        margin: 0;
        width: 27px;
        height: 22px;
        z-index: 27;
        cursor: pointer;
    }

    .masthead__burger-lines {
        position: absolute;
        background: rgba($black, 1);
        width: 100%;
        height: 2px;
        top: 10px;
        right: 0;
        opacity: 1;
        border-radius: 0;

        @media only screen and (max-width: 1160px) {
            background: rgba($white, 1);
        }

        &:before {
            position: absolute;
            background: rgba($black, 1);
            width: 100%;
            height: 2px;
            top: 10px;
            left: 0;
            content: "";
            display: block;
            border-radius: 0;

            @media only screen and (max-width: 1160px) {
                background: rgba($white, 1);
            }
        }

        &:after {
            position: absolute;
            background: rgba($black, 1);
            width: 100%;
            height: 2px;
            bottom: 10px;
            left: 0;
            content: "";
            display: block;
            border-radius: 0;

            @media only screen and (max-width: 1160px) {
                background: rgba($white, 1);
            }
        }
    }

    .masthead__burger-lines,
    .masthead__burger-lines::after,
    .masthead__burger-lines::before {
        transition: all 0.3s ease-in-out;
        -webkit-transition: all 0.3s ease-in-out;
    }
}
