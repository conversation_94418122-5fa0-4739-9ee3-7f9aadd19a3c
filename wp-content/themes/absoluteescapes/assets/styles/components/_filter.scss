.filter {

    @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
        display: none;

    }

    @media only screen and (min-width: map-get($grid-breakpoints, lg)) {
        display: block!important;
    }

    &__inner {
        max-width: 225px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            max-width: 300px;
            padding-top: 30px;
            padding-bottom: 15px;
            margin: 0 auto;

        }
    }

    &__heading {
        padding-bottom: 12px;
    }

    &__heading, &__label {
        font-size: 1.8rem;
        font-weight: 600;
        color: $bluegrey;
    }

    &__label-wrapper {
        display: block;
        position: relative;
        padding: 14px 0;
        margin: 5px 0;
        border-top: 1px solid $bluegrey;
        border-bottom: 1px solid $bluegrey;
        cursor: pointer;

        &.collapsed {
            svg {
                transform: rotate(180deg);
            }
        }

        svg {
            position: absolute;
            top: 0;
            right: 5px;
            bottom: 0;
            margin: auto 0;
            color: $teal;
            transition: 300ms;
            pointer-events: none;
        }

    }

    &__input-wrapper {
        padding: 11px 0;
        input {
            + label {
                margin: 0;
            }
        }

        &--range {
            padding: 20px 10px;
        }
    }

    &__range {
        padding-top: 10px;
        color: $bluegrey;
    }

}
