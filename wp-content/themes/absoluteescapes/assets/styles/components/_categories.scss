.categories {

    &__inner {
        position: relative;
        padding: 25px 0 65px;
        background: $bluegrey;

        &:after {
            content: '';
            display: block;
            position: absolute;
            bottom: 0;
            left: 0;
            z-index: 1;
            width: 100%;
            height: 45px;
            background: url(../img/banner-mask.svg) center top no-repeat;
            background-size: 100% 100%;

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                height: 20px;
            }
        }
    }

    &__content {
        position: relative;
        z-index: 5;
    }


    &__item {
        display: inline-block;
        vertical-align: middle;
        margin: 0 15px;
        font-size: 1.9rem;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            margin: 0 6px;
            font-size: 1.6rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            display: block;
            margin: 5px auto;
            text-align: center;
        }

        &:first-child {
            margin-left: 0;
            font-family: $headinglightsfontfamily;
            font-weight: 500;

        }
        

        a {
            text-decoration: none;
            color: $white;

            &:hover, &:focus {
                color: $teal;
            }
        }
    }

    &__link {
        &.active {
            position: relative;
            color: $teal;

            &:before {
                content: '';
                display: inline-block;
                position: absolute;
                top: 0;
                bottom: 0;
                left: -23px;
                width: 16px;
                height: 11px;
                margin: auto 0;
                background: url(../img/arrow.svg) center no-repeat;
                background-size: contain;
            }
        }
    }
}