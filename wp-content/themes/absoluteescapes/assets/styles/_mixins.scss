@mixin opacity($opacity) {
  opacity: $opacity;
  $opacity-ie: ($opacity * 100);
  filter: #{alpha(opacity=$opacity-ie)};
}

@mixin border-radius($radius) {
  -webkit-border-radius: $radius;
  -moz-border-radius: $radius;
  -ms-border-radius: $radius;
  border-radius: $radius;
}

@mixin box-shadow($top, $left, $blur, $color, $inset: "") {
  -webkit-box-shadow: $top $left $blur $color #{$inset};
  -moz-box-shadow: $top $left $blur $color #{$inset};
  box-shadow: $top $left $blur $color #{$inset};
}

@mixin no-format {
  list-style: none;
  padding: 0;
  margin: 0;
}

@mixin indent {
  text-transform: capitalize;
  text-align: left;
  text-indent: -99999px;
}

@mixin rel {
  position: relative;
}

@mixin box {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

@mixin clearfix {
  &:before,
  &:after {
    content: " ";
    display: table;
  }
  &:after {
    clear: both;
  }
}

@mixin descending-z-index($count: 50) {
  position: relative;
  .row {
    position: relative;
    $target: 0;
    $index: $count;
    @while $index > 0 {
      &:nth-child(#{$target}) {
        z-index: #{$index};
      }
      $target: $target + 1;
      $index: $index - 1;
    }
  }
}

@mixin descending-order($count: 50) {
  $target: $count;
  $index: 0;
  @while $index < $target {
    &:nth-child(#{$index}) {
      order: #{$index};
    }
    $target: $target - 1;
    $index: $index + 1;
  }
}

@mixin increment-delay($start: 0, $count: 100) {
  $target: $count;
  $index: 0;
  @while $start < $target {
    &:nth-child(#{$index}) {
      transition-delay: #{$start * 100}ms;
    }
    $target: $target - 1;
    $index: $index + 1;
    $start: $start + 1;
  }
}

@mixin one-word-per-line {
  width: min-intrinsic;
  width: -webkit-min-content;
  width: -moz-min-content;
  width: min-content;
  display: table-caption;
  display: -ms-grid;
  -ms-grid-columns: min-content;
}

@mixin letter-spacing($font-size, $letter-spacing) {
  letter-spacing: #{$letter-spacing / $font-size}em;
}

/* Custom Mixins
================ */

@mixin brandbutton {
  display: inline-block;
  position: relative;
  min-width: 150px;
  padding: 15px 30px;
  border: none;
  border-radius: 5px;
  text-align: center;
  line-height: 1;
  font-family: $headingfontfamily;
  font-size: 2rem;
  font-weight: 500;
  text-decoration: none;
  background: $teal;
  color: $white;
  outline: 0!important;
  box-shadow: none;

  svg {
    margin-left: 12px;
    font-size: 1.4rem;
  }

  &:hover,
  &:focus {
    text-decoration: none;
    border-color: $white;
    background: $white;
    color: $teal;
  }

  &--alt {
    &:hover, &:focus {
      background: $blue;
      color: $white;
    }
  }


  &--alt-arrow {
    padding: 11px 20px;
    font-size: 1.8rem;
    background: $white;
    border: 1px solid $bluegrey;
    color: $bluegrey;


    &:hover, &:focus {
      border-color: $teal;
      background: $teal;
      color: $white;
    }
  }

  &--hollow {
    border: 1px solid $white;
    background: transparent;
  }

  &--hollow-grey {
    border: 1px solid $bluegrey;
    background: transparent;
    color: $bluegrey;

    &:hover, &:focus {
      border-color: $teal;
      background: $teal;
      color: $white;
    }
  }

  &--icon {
    svg {
      font-size: 1.8rem;
      margin-left: 0;
      margin-right: 10px;
    }
  }
}
