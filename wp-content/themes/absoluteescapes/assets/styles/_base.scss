/* base styles: opinionated defaults */
* {
  box-sizing: border-box;
}

html {
  color: #222;
  font-size: 62.5%;
  line-height: 1.4;
}

body {
  font-size: 1.6em;
}

::-moz-selection {
  background: #b3d4fc;
  text-shadow: none;
}

::selection {
  background: #b3d4fc;
  text-shadow: none;
}

hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #ffd200;
  margin: 1em 0;
  padding: 0;
}

audio,
canvas,
img,
svg,
video {
  vertical-align: middle;
}

fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}

textarea {
  resize: vertical;
}

/* browse happy prompt */

.browsehappy {
  margin: 0.2em 0;
  background: #ccc;
  color: #000;
  padding: 0.2em 0;
}

.browserupgrade {
  background: #ff0000;
  padding: 10px 0;
  text-align: center;
  color: #fff;
  margin: -50px 0 50px;

  a {
    color: #fff;
    text-decoration: underline;
  }
}

/* helper classes */

.hidden {
  display: none !important;
  visibility: hidden;
}

.visuallyhidden {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

.visuallyhidden.focusable:active,
.visuallyhidden.focusable:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto;
}

.invisible {
  visibility: hidden;
}
