.single-holiday {
    .mastfoot {
        padding-bottom: 65px;
    }
}

.mastfoot {
    padding-bottom: 25px;
    background: $bluegrey;
    &__inner {
        padding-top: 70px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            max-width: 520px;
            padding: 50px 15px 0;
            margin: 0 auto;
        }
    }

    .gform_confirmation_wrapper {
        padding: 5px 0 30px;
        text-align: left;
        font-size: 1.6rem;
        color: $white;
    }

    a {
        font-weight: 400;
    }

    &__nav {
        > ul {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -5px;

            > li {
                flex: 1 0;
                padding: 0 5px;
                line-height: 1;
                display: block;
                margin-bottom: 20px;

                @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                    flex: 0 0 100%;
                }

                > a {
                    display: inline-block;
                    position: relative;
                    font-weight: bold;
                    color: $white;
                    cursor: default;

                    &.active {
                        &:after {
                            transform: rotate(-135deg);
                        }
                    }

                    &:hover,
                    &:focus {
                        color: $white;
                    }

                    @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                        &:after {
                            content: "";
                            display: inline-block;
                            position: absolute;
                            top: 4px;
                            right: -12px;
                            width: 6px;
                            height: 6px;
                            margin: auto 0;
                            border-bottom: 2px solid $white;
                            border-right: 2px solid $white;
                            transform: rotate(45deg);
                            transition: 300ms;
                        }
                    }
                }

                ul {
                    li {
                        &:first-child {
                            margin-top: 20px;
                        }
                    }
                }

                .sub-menu {
                    @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                        display: none;
                    }
                    @media only screen and (min-width: map-get($grid-breakpoints, md)) {
                        display: block !important;
                        height: auto !important;
                    }
                }
            }
        }

        ul {
            list-style: none;
            padding: 0;
        }

        li {
            margin: 0 0 5px;
            line-height: 1.4;
            font-size: 1.4rem;
        }

        a {
            color: $white;
            text-decoration: none;

            &:hover,
            &:focus {
                text-decoration: none;
                color: $teal;
            }
        }
    }

    &__row {
        display: flex;
        flex-wrap: wrap;

        &--navigation {
            position: relative;
            padding-top: 50px;

            &:before {
                content: "";
                display: block;
                position: absolute;
                top: 0;
                left: 15px;
                width: calc(100% - 30px);
                border-top: 1px solid $white;
            }
        }

        &--contact-top {
            align-items: center;
            padding-bottom: 40px;

            @media only screen and (max-width: 1400px) {
                padding-bottom: 20px;
                justify-content: center;
                text-align: center;

                .mastfoot__logo-image {
                    margin-bottom: 15px;
                }
            }
        }

        &--contact {
            padding-bottom: 40px;
            border-bottom: 1px solid $white;
            justify-content: center;

            @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                padding-bottom: 20px;
                text-align: center;
            }
        }

        &--details {
            justify-content: space-between;
            padding: 10px 0 80px;

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                padding-bottom: 30px;
            }
        }
    }

    &__navigation {
        flex: 1 0;
        padding: 0 15px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }

    &__form-wrapper {
        flex: 0 0 175px;
        padding: 0 15px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            flex: 0 0 100%;
            max-width: 100%;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 30px 15px;
        }
    }

    &__form-heading {
        display: block;
        margin-bottom: 25px;
        line-height: 1;
        font-size: 1.4rem;
        font-weight: 700;
    }

    &__form-description {
        margin-bottom: 40px;
        line-height: 1.75;
        font-size: 1.4rem;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            margin-bottom: 20px;
        }
    }

    &__form {
        input {
            width: 175px;
            height: 40px;
            padding: 0 10px;
            border-color: $white;
            font-size: 1.4rem;
            background: none;
        }

        .gform_wrapper {
            max-width: none;
            text-align: left;

            .validation_error {
                display: none;
                margin: 0;
                text-align: left;
                font-size: 1.4rem;
            }


            .validation_message {
                text-align: left;
                font-size: 1.4rem;
            }

            .gfield_label {
                font-size: 1.4rem;
                color: $white;
            }

            input {
                color: $white;
            }

            .gfield_required {
                display: none;
            }

            .gform_button {
                margin-top: 0;
                border: none;
                border-radius: 5px;
                font-size: 2rem;
                background: $teal;

                @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                    width: 100%;
                    max-width: 275px;
                }

                &:hover,
                &:focus {
                    color: $white;
                    background: $blue;
                }
            }
        }

        input[type="checkbox"], input[type="radio"] {
            + label {
                padding-left: 25px;
                line-height: 1;
                font-size: 1.4rem;
                color: $white;

                a {
                    color: $white;

                    &:hover, &:focus {
                        color: $teal;
                    }
                }

                &:before {
                    top: 1px;
                    width: 13px;
                    height: 13px;
                    border: 1px solid $white;
                    border-radius: 0;
                    box-sizing: border-box;
                    cursor: pointer;
                    background: transparent;
                }
            }

            &:checked {
                + label {
                    &:after {
                        left: 5px;
                        top: 3px;
                        width: 3px;
                        height: 8px;
                        border: solid $white;
                        border-width: 0 1px 1px 0;
                    }

                    &:before {
                        border-color: $white;
                    }
                }
            }
        }

        input[type="radio"] {

            + label {

                &:before {
                    border-radius: 50%;
                }
            }

            &:checked {
                + label {
                    &:after {
                        content: "";
                        display: inline-block;
                        position: absolute;
                        left: 4px;
                        top: 5px;
                        margin: 0;
                        width: 5px;
                        height: 5px;
                        background: $white;
                        border-radius: 50%;
                        vertical-align: middle;
                    }
                }
            }
        }

        ::placeholder {
            font-style: italic;
            color: #b5b5b5;
            opacity: 1;
        }

        :-ms-input-placeholder {
            font-style: italic;
            color: #b5b5b5;
        }

        ::-ms-input-placeholder {
            font-style: italic;
            color: #b5b5b5;
        }
    }

    &__contact {
        @media only screen and (max-width: 1400px) {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }

    &__contact-item {
        display: inline-block;
        vertical-align: middle;
        margin: 0 10px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            display: block;
            margin: 0 0 20px;
        }

        &:last-child {
            margin-right: 0;
        }
    }

    &__logos {
        text-align: center;
        padding: 0 10px;

        img {
            display: inline-block;
            padding: 6px;
        }

        > div {
            display: inline-block;
            max-width: 100px;
        }
    }

    .mastfoot__link {
        display: block;
        padding: 10px 30px;
        border-radius: 5px;
        border: 1px solid $white;
        line-height: 1;
        font-size: 1.6rem;
        font-family: $headingfontfamily;
        font-weight: 500;
        background: transparent;
        text-decoration: none;
        color: $white;

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            width: 100%;
            max-width: 275px;
            margin: 0 auto;
        }

        svg {
            line-height: 1;
            margin-right: 6px;
            margin-left: 0;
            font-size: 1.3rem;
        }

        &:hover,
        &:focus {
            text-decoration: none;
            background: $white;
            color: $teal;
        }
    }

    &__social-link {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        margin: 0 2px 10px;
        border-radius: 50%;
        border: 1px solid $white;
        font-size: 2rem;
        color: $white;

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            width: 64px;
            height: 64px;
            font-size: 2.6rem;
        }

        @media only screen and (max-width: 365px) {
            width: 45px;
            height: 45px;
            font-size: 2.2rem;
        }

        &:hover,
        &:focus {
            text-decoration: none;
            background: $white;
        }
    }

    &__copyright,
    &__agency {
        font-size: 1.2rem;

        a {
            color: $white;
            text-decoration: none;

            &:hover,
            &:focus {
                text-decoration: none;
                opacity: 0.75;
            }
        }
    }

    &__agency {
        img {
            margin-left: 5px;
            vertical-align: top;
        }
    }

    &__logo {
        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            flex: 0 0 100%;
            max-width: 100%;
            padding-top: 30px;
            margin-bottom: 15px;
            order: 2;
        }
        a {
            display: inline-block;
            max-width: 100px;
            width: 20%;
            &:not(:last-child){
                margin-right: 5px;
            }
        }
    }

    &__contact {
        padding: 10px 0;
        margin-left: auto;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            margin: 0 auto;
        }
    }

    &__col {
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            flex: 0 0 100%;
            max-width: 100%;
            padding: 5px 0;
            text-align: center;
        }
    }
}
