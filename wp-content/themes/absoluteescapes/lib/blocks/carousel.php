<?php

/**
 * Carousel
 */

?>


<section class="carousel">
    <div class="carousel__inner" data-aos="fade">
        <div class="container carousel__container">
            <div class="carousel__content inner-container">
                <?php if(have_rows('images')) : ?>
                    <div class="carousel__images">
                        <?php while(have_rows('images')) : the_row(); ?>
                            <?php 
                            
                            $image = wp_get_attachment_image(get_sub_field('image'), 'carousel');
                            
                            ?>

                            <?php if($image) : ?>
                                <div class="carousel__image">
                                    <?php echo $image; ?>
                                </div>
                            <?php endif; ?>

                        <?php endwhile; ?>
                    </div>

                    <span class="carousel__button carousel__button--prev flickity-icon flickity-icon--prev"><i class="fal fa-chevron-double-left"></i></span>
                    <span class="carousel__button carousel__button--next flickity-icon flickity-icon--next"><i class="fal fa-chevron-double-right"></i></span>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section><!-- .carousel -->