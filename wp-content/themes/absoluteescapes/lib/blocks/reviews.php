<?php 

/**
 * Reviews
 */

$heading = get_sub_field('heading');
$copy = get_sub_field('copy');
$embed = get_sub_field('embed');
$graphic = get_sub_field('reviews_graphic_fallback');
$tooltip = get_sub_field('tooltip');

?>

<section class="reviews">
    <div class="reviews__inner" data-aos="fade">
        <div class="container reviews__container">
            <div class="reviews__content inner-container centre">
                <?php if($heading) : ?>
                    <h2 class="reviews__heading h2-large heading-light heading-underlined text-weight-regular"><?php echo $heading; ?></h2>
                <?php endif; ?>
                <?php if($copy) : ?>
                    <div class="reviews__copy content-area">
                        <?php echo $copy; ?>
                        <?php if($tooltip) : ?>
                            <span class="tooltip"><img src="<?php echo get_stylesheet_directory_uri() . '/dist/img/info.svg' ?>" alt="info"><span class="tooltip__label"><?php echo $tooltip; ?></span></span>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
                <?php if($embed) : ?>
                    <?php echo $embed; ?>
                <?php else : ?>
                    <?php if(have_rows('reviews_fallback')) : ?>
                        <div class="reviews__row">
                            <?php while(have_rows('reviews_fallback')) : the_row(); ?>
                                <?php 
                                
                                $rating = get_sub_field('rating');
                                $rheading = get_sub_field('heading');
                                $copy = get_sub_field('copy');
                                $details = get_sub_field('details');
                                
                                ?>
                                <div class="reviews__col">
                                    <div class="reviews__content">
                                        <div class="reviews__rating font-zero">
                                            <?php for($i = 0; $i < $rating; $i++) : ?>
                                                <span class="reviews__rating-star"><i class="fas fa-star"></i></span>
                                            <?php endfor; ?>
                                        </div>
                                        <?php if($rheading) : ?>
                                            <h6 class="reviews__review-heading"><?php echo $rheading; ?></h6>
                                        <?php endif; ?>
                                        <?php if($copy) : ?>
                                            <div class="reviews__review-copy">
                                                <?php echo $copy; ?>
                                            </div>    
                                        <?php endif; ?>
                                        <?php if($details) : ?>
                                            <div class="reviews__details text-blue">
                                                <span><strong><?php echo $details; ?></strong></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>    
                                </div>
                            <?php endwhile; ?>
                        </div>

                        <span class="reviews__button reviews__button--prev flickity-icon flickity-icon--prev"><i class="fal fa-chevron-double-left"></i></span>
                        <span class="reviews__button reviews__button--next flickity-icon flickity-icon--next"><i class="fal fa-chevron-double-right"></i></span>
                    <?php endif; ?>
                    <?php if($graphic) : ?>
                        <div class="reviews__graphic">
                            <img src="<?php echo $graphic['url']; ?>" alt="<?php echo $graphic['alt']; ?>">
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
           
        </div>
    </div>
</section><!-- .reviews -->