<?php

/**
 * Holidays
 */

$heading = get_sub_field('heading');
$holidays = get_sub_field('holidays');

?>

<section class="holidays">
    <div class="holidays__inner">
        <div class="container holidays__container">
            <div class="holidays__content inner-container">
                <?php if ($heading) : ?>
                    <h2 class="holidays__heading h2-large heading-light heading-underlined text-weight-regular centre"><?php echo $heading; ?></h2>
                <?php endif; ?>
                <?php if ($holidays) : ?>
                    <div class="holidays__posts">
                        <?php foreach ($holidays as $holiday) : ?>
                            <?php

                            $price = get_field('holiday_price', $holiday);
                            $price_suffix_short = get_field('holiday_price_suffix_short', $holiday);
                            $distance = get_field('holiday_distance', $holiday);
                            $min_duration = get_field('holiday_minimum_duration', $holiday);
                            $max_duration = get_field('holiday_maximum_duration', $holiday);
                            if($max_duration == 999) {
                                $max_duration = '';
                            }


                            if($min_duration === $max_duration) {
                                $max_duration = '';
                            }
                            $types = get_the_terms($holiday, 'holiday-type');

                            ?>

                            <div class="holidays__post" data-aos="fade">
                                <div class="row holidays__post-row">
                                    <div class="col-md-5 holidays__post-col">
                                        <?php if (have_rows('holiday_gallery', $holiday)) : ?>
                                            <div class="holidays__gallery">
                                                <?php while (have_rows('holiday_gallery', $holiday)) : the_row(); ?>
                                                    <?php

                                                    $image = wp_get_attachment_image(get_sub_field('image'), 'carousel-small');

                                                    ?>

                                                    <?php if ($image) : ?>
                                                        <div class="holidays__image">
                                                            <?php echo $image; ?>
                                                        </div>
                                                    <?php endif; ?>

                                                <?php endwhile; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-4 holidays__post-col">
                                        <div class="holidays__post-content">
                                            <a href="<?php echo get_the_permalink($holiday); ?>">
                                                <h4 class="holidays__title"><?php echo get_the_title($holiday); ?></h4>
                                            </a>
                                            <?php if ($price) : ?>
                                                <h4 class="holidays__price h4-small">
                                                    <span><?php _e('From ', 'absoluteescapes'); ?><?php echo $price; ?><?php echo $price_suffix_short; ?></span>
                                                </h4>
                                            <?php endif; ?>

                                            <?php if ($types) : ?>
                                                <div class="holidays__types">
                                                    <?php foreach ($types as $type) : ?>
                                                        <?php

                                                        $icon = get_field('holiday_type_icon', $type);
                                                        $icon_fa = get_field('holiday_type_icon_fa', $type);
                                                        $colour = get_field('holiday_type_colour', $type);


                                                        if($type->parent) {
                                                            continue;
                                                        }

                                                        ?>

                                                        <div class="holidays__type cat">
                                                            <?php if ($icon) : ?>
                                                                <span class="holidays__icon cat__icon"
                                                                      style="background-color: <?php echo ($colour) ? $colour : '#3e5056'; ?>"><img
                                                                            src="<?php echo $icon['url']; ?>"
                                                                            alt="<?php echo $icon['alt']; ?>"></span>
                                                            <?php else : ?>
                                                                <?php if ($icon_fa) : ?>
                                                                    <span class="holidays__icon cat__icon"
                                                                          style="background-color: <?php echo ($colour) ? $colour : '#3e5056'; ?>"><i
                                                                                class="<?php echo $icon_fa; ?>"></i></span>
                                                                <?php endif; ?>
                                                            <?php endif; ?>
                                                            <span class="holidays__type-text cat__text"><?php echo $type->name; ?></span>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>

                                            <?php if ($distance) : ?>
                                                <div class="holidays__text">
                                                    <span><?php _e('Distance ', 'absoluteescapes'); ?><?php echo $distance; ?></span>
                                                </div>
                                            <?php endif; ?>

                                            <?php if ($min_duration) : ?>
                                                <div class="holidays__text">
                                                    <span><?php _e('Duration ', 'absoluteescapes'); ?><?php echo $min_duration; ?><?php if($max_duration) : ?><?php echo ($max_duration) ? ' - ' . $max_duration : ''; ?><?php endif; ?><?php _e(' Nights', 'absoluteescapes'); ?></span>
                                                </div>
                                            <?php endif; ?>

                                        </div>
                                    </div>
                                    <div class="col-md-3 holidays__post-col">
                                        <?php if (have_rows('holiday_key_points', $holiday)) : ?>
                                            <div class="holidays__key-points">
                                                <ul class="holidays__key-points-list">
                                                    <?php while (have_rows('holiday_key_points', $holiday)) : the_row(); ?>
                                                        <?php

                                                        $point = get_sub_field('point');

                                                        ?>

                                                        <?php if ($point) : ?>
                                                            <li class="holidays__key-point">
                                                                <span><?php echo $point; ?></span>
                                                            </li>
                                                        <?php endif; ?>

                                                    <?php endwhile; ?>
                                                </ul>
                                            </div>
                                        <?php endif; ?>
                                        <div class="holidays__link-wrapper">
                                            <a href="<?php echo get_the_permalink($holiday); ?>"
                                               class="holidays__link button button--alt-arrow"><?php _e('Find out more', 'absoluteescapes'); ?>
                                                <i class="fas fa-chevron-right"></i></a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section><!-- .holidays -->
