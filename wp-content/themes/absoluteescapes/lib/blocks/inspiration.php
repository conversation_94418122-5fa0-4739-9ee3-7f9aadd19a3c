<?php

/**
 * Inspiration
 */

$heading = get_sub_field('heading');
$copy = get_sub_field('copy');
$posts = get_sub_field('posts');
$categories = get_sub_field('categories');

if(!$posts) {

    $args = [
        'post_type'     => 'post',
        'post_status'   => 'publish',
        'numberposts'   => 4,


    ];

    if($categories) {
        $args['category__in'] = $categories;
    }

    $posts = get_posts($args);
}

?>

<section class="inspiration">
    <div class="inspiration__inner">
        <div class="container inspiration__container">
            <div class="inspiration__content inner-container centre">
                <?php if($heading) : ?>
                    <h2 class="inspiration__heading h2-large heading-light heading-underlined text-weight-regular"><?php echo $heading; ?></h2>
                <?php endif; ?>
                <?php if($copy) : ?>
                    <div class="inspiration__copy content-area">
                        <?php echo $copy; ?>
                    </div>
                <?php endif; ?>
            </div>
            <?php if($posts) : ?>
                <div class="inspiration__posts">
                    <?php foreach($posts as $p) : ?>
                        <?php

                        $thumbnail = wp_get_attachment_image(get_field('post_listing_image', $p->ID), 'destination');

                        ?>

                        <div class="inspiration__post" data-aos="fade">
                            <a href="<?php echo get_the_permalink($p->ID); ?>">
                            <div class="row inspiration__post-row">
                                <div class="col-8 col-md-9 inspiration__post-col">
                                    <h3 class="inspiration__heading"><?php echo get_the_title($p->ID); ?></h3>
                                    <?php if(has_excerpt($p)) : ?>
                                        <div class="inspiration__excerpt content-area">
                                            <p><?php echo get_the_excerpt($p); ?> <span class="link desktop-only"><span class="link-text"><?php _e('Read more', 'absoluteescapes'); ?></span></span></p>
                                        </div>
                                    <?php endif; ?>

                                    <span class="link mobile-only"><span class="link-text"><?php _e('Read more', 'absoluteescapes'); ?></span> <i class="fas fa-chevron-right"></i></span>
                                </div>
                                <div class="col-4 col-md-3 inspiration__post-col">
                                    <?php if($thumbnail) : ?>
                                        <div class="inspiration__thumbnail">
                                            <?php echo $thumbnail; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section><!-- .inspiration -->
