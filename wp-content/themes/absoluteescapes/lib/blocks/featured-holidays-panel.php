<?php
/**
 * Featured Holidays Panel Block
 * Page builder block wrapper for the featured holidays panel component
 */

// Get the panel settings from the current block
$panel_settings = get_sub_field('featured_holidays_panel_settings');

if (!$panel_settings || !$panel_settings['panel_status']) {
    return;
}

$panel_title = $panel_settings['panel_title'] ?: 'Our specialists recommend';
$featured_holidays = $panel_settings['featured_holidays'];

if (!$featured_holidays || empty($featured_holidays)) {
    return;
}

?>

<section class="featured-holidays-panel container">
    <div class="featured-holidays-panel__inner" data-aos="fade">
        <div class="featured-holidays-panel__container">
            <div class="featured-holidays-panel__content">
                <?php if ($panel_title) : ?>
                    <h2 class="featured-holidays-panel__heading heading-light heading-underlined text-weight-regular centre"><?php echo esc_html($panel_title); ?></h2>
                <?php endif; ?>
                
                <div class="featured-holidays-panel__grid" data-count="<?php echo count($featured_holidays); ?>">
                    <?php foreach ($featured_holidays as $item) : ?>
                        <?php 
                        $holiday = $item['holiday'];
                        if (!$holiday) {
                            continue;
                        }
                        
                        $thumbnail = get_the_post_thumbnail_url($holiday->ID);
                        $price = get_field('holiday_price', $holiday->ID);
                        $price_suffix = get_field('holiday_price_suffix', $holiday->ID);
                        $types = get_the_terms($holiday->ID, 'holiday-type');
                        ?>
                        
                        <div class="featured-holidays-panel__tile">
                            <a href="<?php echo get_the_permalink($holiday->ID); ?>" class="featured-holidays-panel__link">
                                <?php if ($thumbnail) : ?>
                                    <div class="featured-holidays-panel__image" style="background-image: url(<?php echo esc_url($thumbnail); ?>);">
                                        <div class="featured-holidays-panel__overlay">
                                            <div class="featured-holidays-panel__content-wrapper">
                                                <h5 class="featured-holidays-panel__title"><?php echo esc_html(get_the_title($holiday->ID)); ?></h5>
                                                <?php if ($price) : ?>
                                                    <div class="featured-holidays-panel__price">
                                                        <span><?php _e('From', 'absoluteescapes'); ?> <?php echo $price; ?><?php if ($price_suffix) { echo ' ' . $price_suffix; } ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <?php if ($types) : ?>
                                                    <div class="featured-holidays-panel__types">
                                                        <?php foreach ($types as $type) : ?>
                                                            <?php
                                                            // Skip child terms, only show parent terms
                                                            if ($type->parent) {
                                                                continue;
                                                            }
                                                            
                                                            $icon = get_field('holiday_type_icon', $type);
                                                            $icon_fa = get_field('holiday_type_icon_fa', $type);
                                                            $colour = get_field('holiday_type_colour', $type);
                                                            ?>
                                                            
                                                            <div class="featured-holidays-panel__type">
                                                                <?php if ($icon) : ?>
                                                                    <div class="featured-holidays-panel__icon" style="background-color: <?php echo esc_attr($colour); ?>;">
                                                                        <img src="<?php echo esc_url($icon['url']); ?>" alt="<?php echo esc_attr($icon['alt']); ?>">
                                                                    </div>
                                                                <?php elseif ($icon_fa) : ?>
                                                                    <div class="featured-holidays-panel__icon" style="background-color: <?php echo esc_attr($colour); ?>;">
                                                                        <i class="<?php echo esc_attr($icon_fa); ?>"></i>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</section>
