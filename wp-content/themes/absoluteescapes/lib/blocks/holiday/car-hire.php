<?php

/**
 * Car Hire
 */

$navigtaion_label = seoUrl(get_sub_field('navigation_label'));
$heading = get_sub_field('heading');
$intro_copy = get_sub_field('intro_copy');
$mid_copy = get_sub_field('mid_copy');
$footer_copy = get_sub_field('footer_copy');
$bg_colour = seoUrl(get_sub_field('background_colour'));

?>


<section id="<?php echo $navigtaion_label; ?>"
         class="car-hire <?php if ($bg_colour) : ?>bg-<?php echo $bg_colour; ?><?php endif; ?>">
    <div class="car-hire__inner" data-aos="fade">
        <div class="car-hire__container">
            <div class="car-hire__content">
                <?php if ($heading) : ?>
                    <h2 class="car-hire__heading"><?php echo $heading; ?></h2>
                <?php endif; ?>
                <?php if ($intro_copy) : ?>
                    <div class="car-hire__copy copy-large">
                        <?php echo $intro_copy; ?>
                    </div>
                <?php endif; ?>
                <?php if (have_rows('information_box')) : ?>
                    <?php while (have_rows('information_box')) : the_row(); ?>
                        <?php

                        $iheading = get_sub_field('heading');
                        $icopy = get_sub_field('copy');

                        ?>
                        <?php if ($iheading || $icopy) : ?>
                            <div class="car-hire__info-box">
                                <h3 class="car-hire__info-box-heading"><?php echo $iheading; ?></h3>
                                <?php if ($icopy) : ?>
                                    <div class="car-hire__info-box-copy">
                                        <?php echo $icopy; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    <?php endwhile; ?>
                <?php endif; ?>
                <?php if ($mid_copy) : ?>
                    <div class="car-hire__info-copy">
                        <?php echo $mid_copy; ?>
                    </div>
                <?php endif; ?>

                <?php if (have_rows('options')) : ?>
                    <div class="car-hire__options">
                        <?php while (have_rows('options')) : the_row(); ?>
                            <?php

                            $oheading = get_sub_field('heading');
                            $type = get_sub_field('type');
                            $price = get_sub_field('price');
                            $price_frequency = get_sub_field('price_frequency');
                            $ocopy = get_sub_field('copy');

                            ?>

                            <div class="car-hire__option">
                                <div class="car-hire__option-row flex">
                                    <div class="car-hire__option-col">
                                        <?php if ($oheading) : ?>
                                            <h5 class="car-hire__heading"><?php echo $oheading; ?></h5>
                                        <?php endif; ?>
                                        <?php if ($type) : ?>
                                            <span class="car-hire__text block"><?php echo $type; ?></span>
                                        <?php endif; ?>
                                        <?php if ($price) : ?>
                                            <span class="car-hire__text block"><?php _e('From', 'absoluteescapes'); ?> <span
                                                        class="text-teal"><?php echo $price; ?></span> <?php echo $price_frequency; ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="car-hire__option-col">
                                        <?php if ($ocopy) : ?>
                                            <div class="car-hire__option-copy">
                                                <?php echo $ocopy; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                        <?php endwhile; ?>
                    </div>
                <?php endif; ?>
                <?php if ($footer_copy) : ?>
                    <div class="car-hire__copy content-area">
                        <?php echo $footer_copy; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section><!-- .car-hire -->
