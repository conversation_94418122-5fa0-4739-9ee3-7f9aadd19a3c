<?php

/**
 * accommodation
 */

$navigation_label = seoUrl(get_sub_field('navigation_label'));
$heading = get_sub_field('heading');
$intro_copy = get_sub_field('intro_copy');
$copy = get_sub_field('copy');
$bg_colour = seoUrl(get_sub_field('background_colour'));

?>

<section id="<?php echo $navigation_label; ?>"
         class="accommodation <?php if ($bg_colour) : ?>bg-<?php echo $bg_colour; ?><?php endif; ?>">
    <div class="accommodation__inner" data-aos="fade">
        <div class="accommodation__container">
            <div class="accommodation__content">
                <?php if ($heading) : ?>
                    <h2 class="accommodation__heading"><?php echo $heading; ?></h2>
                <?php endif; ?>
                <?php if (have_rows('images')) : ?>
                    <div class="accommodation__images">
                        <?php while (have_rows('images')) : the_row(); ?>
                            <?php
                            $alt = '';
                            $image = wp_get_attachment_image(get_sub_field('image'), 'carousel');
                            $alt = get_post_meta(get_sub_field('image'), '_wp_attachment_image_alt', true);
                            $dark_caption = get_sub_field('black_caption');
                            ?>

                            <?php if ($image) : ?>
                                <div class="accommodation__image">
                                    <?php echo $image; ?>
                                    <?php if (!empty($alt)): ?>
                                        <span class="alt-caption <?php if ($dark_caption) : ?>alt-caption--dark<?php endif; ?>">
                                            <?php echo $alt; ?>
                                        </span>
                                    <?php endif ?>
                                </div>
                            <?php endif; ?>
                        <?php endwhile; ?>
                    </div>
                    <span class="accommodation__button accommodation__button--prev flickity-icon flickity-icon--prev"><i
                                class="fal fa-chevron-double-left"></i></span>
                    <span class="accommodation__button accommodation__button--next flickity-icon flickity-icon--next"><i
                                class="fal fa-chevron-double-right"></i></span>
                <?php endif; ?>

                <?php if ($intro_copy) : ?>
                    <div class="accommodation__copy copy-large">
                        <?php echo $intro_copy; ?>
                    </div>
                <?php endif; ?>
                <?php if ($copy) : ?>
                    <div class="accommodation__copy">
                        <?php echo $copy; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <?php if (have_rows('rows')) : ?>
            <div class="accommodation__container accommodation__container--table">
                <div>
                    <div class="itineraries-prices__table">
                        <div class="itineraries-prices__table-row itineraries-prices__table-row--heading">

                            <div class="itineraries-prices__table-col">
                                <span><?php _e('Category', 'absoluteescapes'); ?></span>
                            </div>
                            <div class="itineraries-prices__table-col">
                                <span><?php _e('Accommodation type', 'absoluteescapes'); ?></span>
                            </div>
                            <div class="itineraries-prices__table-col">
                                <span><?php _e('Price per person', 'absoluteescapes'); ?></span>
                            </div>
                        </div>
                        <div class="accordion">
                            <?php while (have_rows('rows')) : the_row(); ?>
                                <?php


                                $category = get_sub_field('category');
                                $type = get_sub_field('accommodation_type');
                                $price = get_sub_field('price_per_person');
                                $highlight = get_sub_field('highlight');
                                $itinerary = get_sub_field('itinerary');

                                ?>
                                <div class="accordion__item<?php if ($highlight): echo ' highlight'; endif; ?>">
                                    <div class="accordion__heading-wrapper">
                                        <div class="itineraries-prices__table-row">
                                            <div class="itineraries-prices__table-col">
                                                <span><?php echo $category ?></span>
                                            </div>
                                            <div class="itineraries-prices__table-col">
                                                <span><?php echo $type ?></span>
                                            </div>
                                            <div class="itineraries-prices__table-col">
                                                <span><?php echo $price ?></span>
                                            </div>
                                        </div>
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="accordion__copy-wrapper none">
                                        <?php if (have_rows('accordion_content')) : ?>
                                            <?php while (have_rows('accordion_content')) : the_row(); ?>
                                                <?php

                                                $copy = get_sub_field('copy');

                                                ?>
                                                <div class="itineraries-prices__expanded">
                                                    <div class="itineraries-prices__row flex">
                                                        <div class="itineraries-prices__expanded-col">
                                                            <?php if ($copy) : ?>
                                                                <div class="itineraries-prices__copy">
                                                                    <?php echo $copy; ?>
                                                                </div>
                                                            <?php endif; ?>
                                                            <?php

                                                            $_terms = wp_list_pluck(get_the_terms(get_the_ID(), 'holiday-type'), 'term_id');
                                                            $value = 'No';


                                                            if (in_array(3, $_terms)) {
                                                                $value = 'Yes';
                                                            }

                                                            ?>
                                                            <div class="accordion__button-wrapper">
                                                                <a href="<?php echo get_the_permalink(543) . '?itinerary=' . seoUrl($itinerary) . '&accommodationtype=' . seoUrl($category) . '&holidayid=' . get_the_ID() . '&selfdrive=' . $value; ?>" class="accordion__button button">Enquire Now</a>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        <?php if (have_rows('lists')) : ?>
            <div class="accommodation__container">
                <div>
                    <div class="package-info__lists">
                        <?php while (have_rows('lists')) : the_row(); ?>
                            <?php

                            $heading = get_sub_field('heading');
                            $crosses = get_sub_field('crosses');

                            ?>

                            <?php if ($heading) : ?>
                                <h3><?php echo $heading; ?></h3>
                            <?php endif; ?>
                            <?php if (have_rows('items')) : ?>

                                <ul class="package-info__items <?php if ($crosses) : ?>package-info__items--crosses<?php endif; ?>">
                                    <?php while (have_rows('items')) : the_row(); ?>
                                        <?php

                                        $text = get_sub_field('text');

                                        ?>
                                        <?php if ($text) : ?>
                                            <li class="package-info__item">
                                                <span class="list-arrow <?php if ($crosses) : ?>list-arrow--cross<?php endif; ?>"><?php echo $text; ?></span>
                                            </li>
                                        <?php endif; ?>

                                    <?php endwhile; ?>
                                </ul>
                            <?php endif; ?>

                        <?php endwhile; ?>
                    </div>

                    <?php if (have_rows('additional_information')) : ?>
                        <?php while (have_rows('additional_information')) : the_row(); ?>
                            <?php

                            $heading = get_sub_field('heading');
                            $copy = get_sub_field('copy');

                            ?>
                            <?php if ($heading || $copy) : ?>
                                <div class="accommodation__information">
                                    <div class="accommodation__information-content">
                                        <?php if ($heading) : ?>
                                            <h3 class="accommodation__heading"><?php echo $heading; ?></h3>
                                        <?php endif; ?>
                                        <?php if ($copy) : ?>
                                            <div class="accommodation__copy">
                                                <?php echo $copy; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                </div>
                            <?php endif; ?>

                        <?php endwhile; ?>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section><!-- .accommodation -->
