<?php

/**
 * Private Guided Tours
 */

$navigation_label = seoUrl(get_sub_field('navigation_label'));
$heading = get_sub_field('heading');
$copy = get_sub_field('copy');
$bg_colour = seoUrl(get_sub_field('background_colour'));

?>

<section id="<?php echo $navigation_label; ?>" class="guided-tours <?php if($bg_colour) : ?>bg-<?php echo $bg_colour; ?><?php endif; ?>">
    <div class="guided-tours__inner" data-aos="fade">
        <div class="guided-tours__container">
            <div class="guided-tours__content">
                <?php if($heading) : ?>
                    <h2 class="guided-tours__heading"><?php echo $heading; ?></h2>
                <?php endif; ?>

                <?php if(have_rows('images')) : ?>
                    <div class="accommodation__images">
                        <?php while(have_rows('images')) : the_row(); ?>
                            <?php $image = wp_get_attachment_image(get_sub_field('image'), 'carousel'); ?>

                            <?php if($image) : ?>
                                <div class="accommodation__image">
                                    <?php echo $image; ?>
                                </div>
                            <?php endif; ?>
                        <?php endwhile; ?>
                    </div>
                    <span class="accommodation__button accommodation__button--prev flickity-icon flickity-icon--prev"><i class="fal fa-chevron-double-left"></i></span>
                    <span class="accommodation__button accommodation__button--next flickity-icon flickity-icon--next"><i class="fal fa-chevron-double-right"></i></span>
                <?php endif; ?>

                <?php if($copy) : ?>
                    <div class="guided-tours__copy content-area copy-large">
                        <?php echo $copy; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section><!-- .guided-tours -->
