<?php

/**
 * Create Post Type
 */


function absoluteescapes_instagram_post_type()
{

    $labels = array(
        'name' => __('Instagram'),
        'singular_name' => __('Instagram'),
        'all_items' => __('All Instagram Photos'),
        'add_new' => __('Add New'),
        'add_new_item' => __('Add New'),
        'edit_item' => __('Edit'),
        'new_item' => __('New'),
        'view_item' => __('View'),
        'search_items' => __('Search'),
        'not_found' => __('No Instagram Photos Found'),
        'not_found_in_trash' => __('No Instagram Photos Found in Trash'),
        'parent_item_colon' => ''
    );
    $args = array(
        'labels' => $labels,
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'exclude_from_search' => true,
        'show_in_nav_menus' => false,
        'has_archive' => false,
        'rewrite' => false,
        'menu_icon' => 'dashicons-instagram',
        'menu_position' => 22,
        'hierarchical' => false,
        'supports' => array('title', 'editor', 'thumbnail')
    );
    register_post_type('instagram', $args);
}

add_action('init', 'absoluteescapes_instagram_post_type');


function absoluteescapes_holiday_post_type()
{

    $labels = array(
        'name' => __('Holidays'),
        'singular_name' => __('Holiday'),
        'all_items' => __('All Holidays'),
        'add_new' => __('Add New'),
        'add_new_item' => __('Add New'),
        'edit_item' => __('Edit'),
        'new_item' => __('New'),
        'view_item' => __('View'),
        'search_items' => __('Search'),
        'not_found' => __('No Holidays Found'),
        'not_found_in_trash' => __('No Holidays Found in Trash'),
        'parent_item_colon' => ''
    );
    $args = array(
        'labels' => $labels,
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'exclude_from_search' => false,
        'show_in_nav_menus' => true,
        'has_archive' => true,
        'rewrite' => array('slug' => 'holidays', 'with_front' => false),
        'menu_icon' => 'dashicons-clipboard',
        'menu_position' => 21,
        'hierarchical' => false,
        'supports' => array('title', 'thumbnail', 'excerpt')
    );
    register_post_type('holiday', $args);
}

add_action('init', 'absoluteescapes_holiday_post_type');


/**
 * Create Post Taxonomy
 */

function absoluteescapes_holiday_type_taxonomy()
{

    $labels = array(
        'name' => _x('Types', 'taxonomy general name'),
        'singular_name' => _x('Type', 'taxonomy singular name'),
        'search_items' => __('Types'),
        'all_items' => __('All Types'),
        'parent_item' => __('Parent Type'),
        'parent_item_colon' => __('Parent Type:'),
        'edit_item' => __('Edit Type'),
        'update_item' => __('Update Type'),
        'add_new_item' => __('Add Type'),
        'new_item_name' => __('New Type'),
        'menu_name' => __('Types'),
    );

    register_taxonomy('holiday-type', array('holiday'), array(
        'hierarchical' => true,
        'labels' => $labels,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
    ));

}
add_action('init', 'absoluteescapes_holiday_type_taxonomy');

function absoluteescapes_holiday_region_taxonomy()
{

    $labels = array(
        'name' => _x('Regions', 'taxonomy general name'),
        'singular_name' => _x('Region', 'taxonomy singular name'),
        'search_items' => __('Region'),
        'all_items' => __('All Region'),
        'parent_item' => __('Parent Region'),
        'parent_item_colon' => __('Parent Region:'),
        'edit_item' => __('Edit Region'),
        'update_item' => __('Update Region'),
        'add_new_item' => __('Add Region'),
        'new_item_name' => __('New Region'),
        'menu_name' => __('Regions'),
    );

    register_taxonomy('holiday-regions', array('holiday'), array(
        'hierarchical' => true,
        'labels' => $labels,
        'show_ui' => true,
        'rewrite' => array('slug' => 'destinations', 'with_front' => false),
        'show_admin_column' => true,
        'query_var' => true,
    ));

}
add_action('init', 'absoluteescapes_holiday_region_taxonomy');