<!-- Call To Action -->
 <?php 
 
 $defaults = [
    'status' => true,
    'title' => 'Your journey starts here',
    'text' => 'We create award-winning holidays rooted in local knowledge. Enquire now for expert advice and a bespoke itinerary tailored just for you.',
    'button_label' => 'Make an Enquiry',
    'button_link' => '/general-enquiry/',
    'icon' => '<svg class="svg-inline--fa fa-phone fa-w-16" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="phone" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z"></path></svg>',
    'contact_number' => '+44 (0) ************',
    'use_image' => true,
    'image' => ['url' => '/wp-content/themes/absoluteescapes/dist/img/holiday-listing-2-bottom-panel.jpg', 'alt' => 'Alt text'],
 ];
 // Check if we have block-level CTA data (from Holiday Listing block)
 global $acf_block_data;
 if (isset($acf_block_data) && is_array($acf_block_data)) {
     $block_cta = isset($acf_block_data['bottom_cta']) ? $acf_block_data['bottom_cta'] : null;
     // Only use block CTA if it has been explicitly configured (status field exists and has content)
     if ($block_cta && is_array($block_cta) && isset($block_cta['status']) && !empty($block_cta['title'])) {
         $bottom_cta = $block_cta;
     } else {
         // Fallback to taxonomy CTA data - check for holiday type from block context
         if (isset($acf_block_data['holiday_type'])) {
             $holiday_type_id = $acf_block_data['holiday_type'];
             if ($holiday_type_id) {
                 $term = get_term($holiday_type_id, 'holiday-type');
                 if ($term && !is_wp_error($term)) {
                     $bottom_cta = get_field('bottom_cta', $term);
                 }
             }
         }

         // Final fallback to queried object (for taxonomy archive pages)
         if (!$bottom_cta) {
             $term = get_queried_object();
             $bottom_cta = get_field('bottom_cta', $term);
         }
     }
 } else {
     // Default behavior for taxonomy pages
     $term = get_queried_object();
     $bottom_cta = get_field('bottom_cta', $term);
 }

 // Debug and fix the data type issue
 if (!isset($bottom_cta) || !is_array($bottom_cta)) {
     $bottom_cta = $defaults;
 }

 // Ensure status is properly set
 if (!isset($bottom_cta['status'])) {
     $bottom_cta['status'] = true;
 }

 if ($bottom_cta['status'] === true) { ?>
<div class="listing-cta cta-bottom">
    <div class="listing-cta__background">
        <div class="container">
            <div class="listing-cta__content">
               <?php if ($bottom_cta['use_image'] === true) { ?>
                <div class="listing-cta__image">
                        <?php if (empty($bottom_cta['image'])) { $bottom_cta['image'] = $defaults['image']; } ?>
                           <img loading="lazy" src="<?php echo $bottom_cta['image']['url'] ?>" alt="<?php echo $bottom_cta['image']['alt'] ?>">    
                </div>
                <?php } ?>
                <div class="listing-cta__text<?php if ($bottom_cta['use_image'] !== true) { echo ' centre'; } ?>">
                    <?php if (!empty($bottom_cta['title'])) { ?>
                        <h2 class="text-block__heading heading-light text-weight-regular"><?php echo $bottom_cta['title']; ?></h2>
                    <?php } ?>
                    <?php if (!empty($bottom_cta['text'])) { ?>
                        <p><?php echo $bottom_cta['text']; ?></p>
                    <?php } ?>
                    <?php if ((!empty($bottom_cta['button_link']) && !empty($bottom_cta['button_label'])) || !empty($bottom_cta['icon']) || !empty($bottom_cta['contact_number'])) { ?>
                        <p class="listing-cta__actions">
                    <?php } ?>
                    <?php if (!empty($bottom_cta['button_link']) && !empty($bottom_cta['button_label'])) { ?>
                     <a href="<?php echo $bottom_cta['button_link']; ?>" class="button"><?php echo $bottom_cta['button_label']; ?></a>
                     <?php } ?>
                     <?php if (!empty($bottom_cta['contact_number'])) { ?>
                      <a href="tel:<?php echo preg_replace('/[^0-9\+]/', '', $bottom_cta['contact_number']); ?>">
                        <?php if (!empty($bottom_cta['icon'])) { ?>
                            <?php echo $bottom_cta['icon']; ?>
                        <?php } ?>
                        <strong><?php echo $bottom_cta['contact_number']; ?></strong>
                       </a>
                     <?php } ?>
                     <?php if ((!empty($bottom_cta['button_link']) && !empty($bottom_cta['button_label'])) || !empty($bottom_cta['icon']) || !empty($bottom_cta['contact_number'])) { ?>
                     </p>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php } ?>
<!-- / Call To Action -->