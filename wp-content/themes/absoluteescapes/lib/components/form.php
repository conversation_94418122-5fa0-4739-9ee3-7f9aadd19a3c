<?php 

/**
 * Form
 */

$holiday_id = sanitize_text_field($_GET['holidayid']);
$start_date = sanitize_text_field($_GET['startdate']);
$itinerary = sanitize_text_field($_GET['itinerary']);
$accommodation_type = sanitize_text_field($_GET['accommodationtype']);
$form_id = get_field('form_id');
$form_back_link = get_field('form_back_link');

?>

<section class="form">
    <div class="form__inner">
        <div class="container form__container">
            <div class="form__content inner-container">
            <?php if($form_back_link) : ?>
                <div class="form__back-link-wrapper">
                    <a href="<?php echo $form_back_link['url']; ?>" <?php echo ($form_back_link['target']) ? 'target="_blank"' : ''; ?> class="form__back-link"><?php echo $form_back_link['title']; ?></a>
                </div>
            <?php endif; ?>

            <div class="form__heading-wrapper">

            </div>
            <?php if($form_id == 2 && $holiday_id) : ?>
                <?php 
                
                $title = get_the_title($holiday_id);
                $thumbnail = get_the_post_thumbnail($holiday_id, 'destination');
                
                ?>
                <div class="form__section" data-id="<?php echo get_the_ID(); ?>">
                    <div class="form__section-header">
                        <h2 class="form__section-title"><?php _e('1. Your travel plans', 'absoluteescapes'); ?></h2>
                    </div>
                    <div class="form__section-content">
                        <?php if($title) : ?>
                            <h5 class="form__heading text-weight-semibold body-text"><?php echo $title; ?></h5>
                        <?php endif; ?>
                        <div class="form__details">
                            <?php if($thumbnail) : ?>
                                <div class="form__detail">
                                    <div class="form__thumbnail">
                                        <?php echo $thumbnail; ?>
                                    </div>
                                </div>    
                            <?php endif; ?>
                            <?php if(have_rows('holiday_itinerary', $holiday_id)) : ?>
                                <div class="form__detail">
                                    <span class="form__detail-heading"><?php _e('Itinerary:', 'absoluteescapes'); ?></span>
                                    <div class="select-wrapper">
                                        <select id="formItinerary">
                                            <?php while(have_rows('holiday_itinerary', $holiday_id)) : the_row(); ?>
                                                <?php
                                                
                                                $option = get_sub_field('option');

                                                ?>
                                                <option value="<?php echo seoUrl($option); ?>" <?php if($itinerary === seoUrl($option)) : ?>selected<?php endif; ?>><?php echo $option; ?></option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>
                                    <span class="form__detail-label">(<?php _e('Tour code:', 'ansoluteescapes'); ?><span class="tour-code"><?php echo get_field('holiday_tour_code', $holiday_id); ?></span>)</span>
                                </div>
                            <?php endif; ?>
                            <div class="form__detail">
                                <span class="form__detail-heading"><?php _e('Start date:', 'absoluteescapes'); ?></span>
                                <div class="select-wrapper">
                                    <input id="formStart" data-toggle="datepicker" placeholder="Select start date" value="<?php echo $start_date; ?>">
                                </div>
                            </div>
                            <?php if(have_rows('holiday_accommodation_types', $holiday_id)) : ?>
                                <div class="form__detail">
                                    <span class="form__detail-heading"><?php _e('Accommodation:', 'absoluteescapes'); ?></span>
                                    <div class="select-wrapper">
                                        <select id="formAccommodation">
                                            <?php while(have_rows('holiday_accommodation_types', $holiday_id)) : the_row(); ?>
                                                <?php
                                                
                                                $option = get_sub_field('type');

                                                ?>
                                                <option value="<?php echo seoUrl($option); ?>" <?php if($accommodation_type === seoUrl($option)) : ?>selected<?php endif; ?>><?php echo $option; ?></option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>
                                </div>
                            <?php endif; ?>

                        </div>
                    </div>
                </div>
            <?php endif; ?>
         
                <?php gravity_form( $form_id, true, true, false, null, true, 20); ?>
            </div>
        </div>
    </div>
</section>