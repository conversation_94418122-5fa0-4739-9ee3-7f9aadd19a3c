const gulp = require('gulp');
const cssnano = require("cssnano");
const autoprefixer = require("autoprefixer");
const sass = require("gulp-dart-sass");
const imagemin = require('gulp-imagemin'); // minify images
const concat = require('gulp-concat'); // concat js
const uglify = require('gulp-uglify'); // compress js
const plumber         = require('gulp-plumber');
const postcss = require("gulp-postcss");
const rename = require('gulp-rename');

function styles(){
	const plugins = [
		autoprefixer({ browsers: ["last 2 versions"] }),
		cssnano()
	];
	return gulp.src('./assets/styles/style.scss')
		.pipe(
			plumber({
				errorHandler: function(err) {
					console.log(err);
					this.emit("end");
				}
			})
		)
		.pipe(sass({ outputStyle: "expanded" }))
		.pipe(postcss(plugins))
		.pipe(gulp.dest('./dist/styles'))
		.pipe(rename({ suffix: ".min" }))
		.pipe(gulp.dest('./dist/styles'));
}

function scripts() {
  return gulp.src(['assets/scripts/custom/*.js'])
    .pipe(uglify())
	.pipe(concat('main.min.js'))
	.pipe(gulp.dest('dist/scripts'))
}

function vendor_scripts() {
  return gulp.src(['assets/scripts/vendor/*.js'])
    .pipe(uglify())
	.pipe(concat('vendor.min.js'))
	.pipe(gulp.dest('dist/scripts'))
}

function images() {
	return gulp.src('assets/src/images/*')
	.pipe(imagemin())
	.pipe(gulp.dest('assets/dist/images'))
}

// Watch Files
function watchFiles() {
	gulp.watch('assets/styles/**/*.scss', styles);
	gulp.watch(['assets/scripts/vendor/*.js','assets/scripts/custom/*.js'], scripts);
	gulp.watch('assets/src/images/*', images);
}

const watch = gulp.parallel(watchFiles);

exports.styles = styles;
exports.scripts = scripts;
exports.images = images;
exports.vendor_scripts = vendor_scripts;
exports.watch = watch;

// Default Task
exports.default = watch;
