"use strict";$(function(){var n=$("input[value='holiday']"),a=$("input[value='tourcode']"),o=$("input[value='itinerary']"),i=$("input[value='accommodation']"),c=$("input[value='startdate']");jQuery(document).on("gform_post_render",function(t,e){1!==e&&($(".gfmc-column").append('<div class="form__next"><span class="button button--alt">Next <i class="fas fa-chevron-right"></i></span><div>'),$(".form").find(".gform_footer").appendTo(".gfmc-column:last"),$(".gform_confirmation_wrapper").length&&($(".form__section").remove(),$(".form__heading-wrapper").remove()),$(".gsection").first().addClass("active"),$(".gsection").first().next().addClass("active"),$(".validation_message").length&&($(".validation_message").closest(".gfmc-column").addClass("active"),$(".validation_message").closest(".gfmc-column").prev().addClass("active"))),2===e&&(n.val($(".form__section").attr("data-id")),a.val($(".tour-code").text()),o.val($("#formItinerary").val()),i.val($("#formAccommodation").val()),c.val($("#formStart").val()),$("#formItinerary").on("change",function(){o.val($(this).val())}),$("#formAccommodation").on("change",function(){i.val($(this).val())}),$("#formStart").on("change",function(){c.val($(this).val())})),$(".validation_error").length&&$(".payment-choice").find("input").attr("checked",!1),$(document).find(".payment-choice").find("input").on("change",function(){"bank transfer"!==$(this).val().toLowerCase()?($(".form--payment").find(".gsection").show(),$(".payment-details__notice").removeClass("active"),$(".gfmc-row-2-column").addClass("active"),$(".gfmc-row-2-column").prev().addClass("active")):($(".form--payment").find(".gsection").hide(),$(".form--payment").find(".gfmc-column").removeClass("active"),$(".form--payment").find(".gsection").removeClass("active"),$(".payment-details__notice").addClass("active"),$(".gsection").first().addClass("active"))})}),gform.addFilter("gform_datepicker_options_pre_init",function(t,e,n){return t.yearRange="-0:+5",t}),jQuery(document).on("gform_confirmation_loaded",function(t,e){2!==e&&3!==e||$(".gform_confirmation_wrapper").length&&($(".form__section").remove(),$(".form__heading-wrapper").remove())}),$(document).on("click",".form__next .button",function(){var t=$(this);$(this).closest(".gfmc-column").removeClass("active"),$(this).closest(".gfmc-column").next().is(":visible")?($(this).closest(".gfmc-column").next().addClass("active"),$(this).closest(".gfmc-column").next().next().addClass("active")):($(this).closest(".gfmc-column").next().next().next().addClass("active"),$(this).closest(".gfmc-column").next().next().next().next().addClass("active")),$("html, body").animate({scrollTop:t.closest(".gfmc-column").next().offset().top},0)}),$(document).on("click",".gsection",function(){$(this).next().hasClass("active")?$(this).next().removeClass("active"):($(this).next().addClass("active"),$(this).addClass("active"))}),$("#filterTrigger").on("click",function(t){t.preventDefault(),$(".filter").slideToggle()}),$(".enquiry-form").on("click",function(t){t.target===this&&$(this).removeClass("active")}),$(".enquiry-cta__button").on("click",function(t){t.preventDefault(),$(".enquiry-form").toggleClass("active")}),$(".enquiry-form__close").on("click",function(){$(".enquiry-form").removeClass("active")})});
"use strict";$(window).on("load",function(){var t=$(".subnav");t.length&&Stickyfill.add(t);var n=$(".enquiry-form");n.length&&Stickyfill.add(n);var i=function t(n){var i,a=window.location.search.substring(1).split("&"),e,o;for(o=0;o<a.length;o++)if((e=a[o].split("="))[0]===n)return void 0===e[1]||decodeURIComponent(e[1])},a=115,e,o;function r(t){var n=t.getBoundingClientRect(),i=window.innerHeight||document.documentElement.clientHeight,a=window.innerWidth||document.documentElement.clientWidth,e=n.top<=i&&0<=n.top+(n.height-116),o=n.left<=a&&0<=n.left+n.width;return e&&o}$(document).on("click",".masthead a",function(t){!/#/.test(this.href)||$(this).hasClass("aito-link")||$(this).hasClass("modal-close")||t.preventDefault()}),$(".aito-link").on("click",function(){$(".review-bar").addClass("active")}),$(".modal-close").on("click",function(){$(".review-bar").removeClass("active")}),$(".scroll-next").on("click",function(){$("html, body").animate({scrollTop:$(this).closest("section").next().offset().top-50},500)}),$(".back-to-top").on("click",function(){$("html, body").animate({scrollTop:$("html").offset().top},1e3)});var l=$(".reviews__row");l.on("ready.flickity",function(){$(this).parent().find(".reviews__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".reviews__button--next").appendTo($(this).find(".flickity-button.next"))}),l.flickity({wrapAround:!0,contain:!0,freeScroll:!0});var s=$(".favourite-holidays__row");s.on("ready.flickity",function(){$(this).parent().find(".favourite-holidays__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".favourite-holidays__button--next").appendTo($(this).find(".flickity-button.next"))}),s.flickity({freeScroll:!0,groupCells:!0});var c=$(".destinations__row");c.on("ready.flickity",function(){$(this).parent().find(".favourite-holidays__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".favourite-holidays__button--next").appendTo($(this).find(".flickity-button.next"))}),c.flickity({wrapAround:!0,pageDots:!1,freeScroll:!0,contain:!0,groupCells:!0,cellAlign:"left"});var d=$(".instagram__photos");d.on("ready.flickity",function(){$(this).parent().find(".instagram__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".instagram__button--next").appendTo($(this).find(".flickity-button.next"))}),d.flickity({pageDots:!1,freeScroll:!0,contain:!0,groupCells:!0,cellAlign:"left"});var u=$(".carousel__images"),f;u.on("ready.flickity",function(){$(this).parent().find(".carousel__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".carousel__button--next").appendTo($(this).find(".flickity-button.next"))}),u.flickity({wrapAround:!0,contain:!0,groupCells:!0}),$(".holidays__gallery").flickity({wrapAround:!0,contain:!0,groupCells:!0,arrowShape:{x0:10,x1:60,y1:50,x2:70,y2:35,x3:35}});var p=$(".accommodation__images");p.on("ready.flickity",function(){$(this).parent().find(".accommodation__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".accommodation__button--next").appendTo($(this).find(".flickity-button.next"))}),p.flickity({wrapAround:!0,contain:!0,groupCells:!0});var v=$(".inf-posts");$(".next-posts-link").length&&v.length&&(v.infiniteScroll({path:".next-posts-link a",append:".inf-posts .inf-post",history:!0,button:".button-inf",scrollThreshold:!1,status:".page-load-status"}),v.on("append.infiniteScroll",function(t,n,i,a){$(a).addClass("appended-item"),v.imagesLoaded(function(){$(a).find("img").each(function(t,n){n.outerHTML=n.outerHTML}),$(document).find(".holidays__gallery").length&&$(document).find(".holidays__gallery").flickity({wrapAround:!0,contain:!0,groupCells:!0,arrowShape:{x0:10,x1:60,y1:50,x2:70,y2:35,x3:35}})})}));var h=document.getElementById("distanceRangeSlider");if($("#distanceRangeSlider").length){noUiSlider.create(h,{start:[1,32],connect:!0,step:1,range:{min:1,max:32}});var _=i("durationmin"),m=i("durationmax");_&&m?h.noUiSlider.set([_,m]):_?h.noUiSlider.set([_,null]):m&&h.noUiSlider.set([null,m]),h.noUiSlider.on("update",function(t,n){$(".filter__range-number--min").text(Math.floor(t[0])),$("#durationMin").val(Math.floor(t[0])),$(".filter__range-number--max").text(Math.floor(t[1])),$("#durationMax").val(Math.floor(t[1]))}),$("#orderDropdown").on("change",function(){$("#sort").val($(this).val()),$("#filterForm").submit()}),h.noUiSlider.on("change",function(){$("#filterForm").submit()})}$(".filter__input").on("change",function(){$("#filterForm").submit()});var b=!1;$(".filter__label-wrapper").on("click",function(){var t=$(this);b||(b=!0,t.hasClass("collapsed")?t.next().slideDown(function(){t.removeClass("collapsed"),b=!1}):t.next().slideUp(function(){t.addClass("collapsed"),b=!1}))});var g=[],y;$(".page-header__gallery").find("img").each(function(){(y={}).src=$(this).attr("src"),g.push(y)}),$("#galleryTrigger").on("click",function(t){t.preventDefault(),$.fancybox.open(g,{loop:!0}),$('[data-fancybox="gallery"]').fancybox({afterLoad:function t(n,i){i.$image.attr("alt",i.opts.$orig.find("img").attr("alt"))}})});var k=!1;$(".accordion__heading-wrapper").on("click",function(){var t=$(this);k||(k=!0,t.parent().hasClass("active")?(t.parent().removeClass("active"),t.next().slideUp(function(){k=!1})):(t.parent().addClass("active"),t.next().slideDown(function(){k=!1})))}),$(".accordion__close").on("click",function(){if(!k){k=!0;var t=$(this);t.parent().parent().slideUp(function(){k=!1,t.parent().parent().parent().removeClass("active")})}}),$("#subNav").on("change",function(){var t=$(this).val();$("html, body").animate({scrollTop:$("#"+t).offset().top-100},0)}),$(".subnav").length&&(setTimeout(function(){$(this).scrollTop()<=150?($(".subnav__link").parent().removeClass("active"),$("#subNav").val("")):$("section").each(function(){if(""!==$(this).prop("id")){var t=$(this);if(r(this))return $(".subnav__link").each(function(){$(this).attr("data-id")===t.prop("id")?($(".subnav__link[data-id="+t.prop("id")+"]").parent().addClass("active"),$("#subNav").val(t.prop("id"))):($(".subnav__link").not(".subnav__link[data-id="+t.prop("id")+"]").parent().removeClass("active"),$("#subNav").val(""))}),!1;$(".subnav__link").not(".subnav__link[data-id="+t.prop("id")+"]").parent().removeClass("active"),$("#subNav").val("")}})},50),$(window).on("scroll",function(){$(this).scrollTop()<=150?($(".subnav__link").parent().removeClass("active"),$("#subNav").val("")):$("section").each(function(){if(""!==$(this).prop("id")){var t=$(this);if(r(this))return $(".subnav__link").each(function(){$(this).attr("data-id")===t.prop("id")?($(".subnav__link[data-id="+t.prop("id")+"]").parent().addClass("active"),$("#subNav").val(t.prop("id"))):$(".subnav__link").not(".subnav__link[data-id="+t.prop("id")+"]").parent().removeClass("active")}),!1;$(".subnav__link").not(".subnav__link[data-id="+t.prop("id")+"]").parent().removeClass("active"),$("#subNav").val("")}})})),$('[data-toggle="datepicker"]').datepicker({format:"dd/mm/yyyy",autoHide:!0}),$(".tooltip").on("click",function(){$(this).toggleClass("active")}),$(".search-trigger").on("click",function(){$(".masthead__form-container").toggleClass("active"),$(".masthead__form-container").hasClass("active")&&$(".masthead__form > input").focus()}),$(".masthead__form-close").on("click",function(){$(".masthead__form-container").removeClass("active")});var x=!1;$(".overview__bottom-copy-trigger").on("click",function(){x||(x=!0,$(this).hasClass("active")?($(this).removeClass("active"),$(this).next().slideUp(function(){x=!1})):($(this).addClass("active"),$(this).next().slideDown(function(){x=!1})))}),$(".banner__slideshow").length&&$(".banner__slideshow").slideshow({randomize:!1,slideDuration:6e3,fadeDuration:1e3,animate:!0,pauseOnTabBlur:!0}),AOS.init({once:!0}),$(".overlay").fadeOut()});
"use strict";$(function(){var a=$(".masthead"),s=$(".masthead__burger"),e=$(".navigation");s.on("click",function(){$(this).toggleClass("active"),$(this).hasClass("active")?(a.addClass("active-nav"),e.addClass("active"),$("html, body").addClass("overflow-menu")):(a.removeClass("active"),e.removeClass("active"),$("html, body").removeClass("overflow-menu"))}),$(".navigation__close").on("click",function(){a.removeClass("active"),e.removeClass("active"),e.removeClass("submenu-active"),$("html, body").removeClass("overflow-menu"),s.removeClass("active"),$(".sub-menu").removeClass("active")}),$(".navigation").find(".menu-item-has-children").append('<span class="sub-arrow"></span>'),$(".navigation").find(".sub-menu").each(function(){var s=$(this).prev().text();$(this).prepend('<li><a class="back-link" href="#">< '+s+"</a></li>")}),$(document).on("click",".sub-arrow",function(){$(this).prev().addClass("active"),e.addClass("submenu-active"),e.scrollTop(0)}),$(document).on("click",".back-link",function(){$(this).parent().parent().removeClass("active"),$(this).parent().parent().parent().parent().hasClass("menu")&&e.removeClass("submenu-active"),e.scrollTop(0)}),$(".navigation").find("a[href*='#']").on("click",function(){$(this).hasClass("back-link")||($(this).next().addClass("active"),e.addClass("submenu-active"),e.scrollTop(0))});var i=!1;$(".mastfoot").find(".menu-item-has-children > a").on("click",function(s){s.preventDefault(),i||(i=!0,$(this).hasClass("active")?($(this).removeClass("active"),$(this).next(".sub-menu").slideUp(function(){i=!1})):($(this).addClass("active"),$(this).next(".sub-menu").slideDown(function(){i=!1})))});var n=0;$(window).on("scroll",function(){if(!a.hasClass("masthead--static")){var s=$(this).scrollTop();150<s?(a.addClass("is-fixed"),n<s?a.hasClass("is-visible")&&(a.addClass("is-hidden"),a.removeClass("is-visible")):(a.removeClass("is-hidden"),a.addClass("is-visible"))):s<1&&(a.removeClass("is-fixed"),a.removeClass("is-hidden"),a.removeClass("is-visible")),n=s}})});
"use strict";$(function(){$(".filter-links").move({breakpoint:991,oldLocation:".holidays-results",newLocation:".filter",methods:{o:"insertBefore",n:"insertAfter"}}),$(".holidays-results__sort-by-wrapper").move({breakpoint:991,oldLocation:".holidays-results__order",newLocation:".holidays-results__sort-by-trigger-wrapper",methods:{o:"appendTo",n:"appendTo"}}),$(".masthead__form-header").move({breakpoint:1160,oldLocation:".masthead__details",newLocation:".form__container",methods:{o:"insertBefore",n:"prependTo"}})});